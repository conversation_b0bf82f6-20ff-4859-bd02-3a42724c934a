package types

// LoginRequest 登录请求
type LoginRequest struct {
	Phone   string `json:"phone" validate:"required,min=11,max=11"`
	SmsCode string `json:"sms_code" validate:"required,min=4,max=6"`
}

// WechatLoginRequest 微信登录请求
type WechatLoginRequest struct {
	LoginCode string `json:"loginCode" validate:"required"`
	PhoneCode string `json:"phoneCode" comment:"手机号授权码，新用户必须提供"`
	DeviceID  string `json:"deviceId" validate:"required" comment:"设备唯一标识"`
}

// SendSmsCodeRequest 发送短信验证码请求
type SendSmsCodeRequest struct {
	Phone string `json:"phone" validate:"required,min=11,max=11"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	AccessToken string    `json:"access_token"`
	User        *UserInfo `json:"user"`
}

// CheckUserRequest 检查用户注册状态请求
type CheckUserRequest struct {
	Phone string `json:"phone" validate:"required,min=11,max=11"`
}

// CheckUserResponse 检查用户注册状态响应
type CheckUserResponse struct {
	IsRegistered bool   `json:"is_registered"`
	Message      string `json:"message"`
}

// CheckUserByCodeRequest 根据code检查用户注册状态请求
type CheckUserByCodeRequest struct {
	Code     string `json:"code" validate:"required"`
	DeviceID string `json:"deviceId" validate:"required" comment:"设备唯一标识"`
}

// CheckUserByCodeResponse 检查用户注册状态响应
type CheckUserByCodeResponse struct {
	IsRegistered bool   `json:"is_registered"`
	HasPhone     bool   `json:"has_phone"` // 是否已绑定手机号
	OpenID       string `json:"openid,omitempty"`
	Message      string `json:"message"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID               uint   `json:"id"`                // 主键ID
	UID              string `json:"uid"`               // 用户唯一标识符
	Phone            string `json:"phone"`             // 手机号
	Nickname         string `json:"nickname"`          // 昵称
	Birthday         *Date  `json:"birthday"`          // 生日
	Avatar           string `json:"avatar"`            // 头像URL
	Source           string `json:"source"`            // 来源：mp-weixin, sms
	Gender           int16  `json:"gender"`            // 性别：0-未知,1-男,2-女
	IsVerified       bool   `json:"is_verified"`       // 是否已认证
	PersonalVerified bool   `json:"personal_verified"` // 个人实名认证状态
	EnterpriseID     uint   `json:"enterprise_id"`     // 企业ID，0表示未关联企业
	Points           int    `json:"points"`            // 积分
}

// // ChangePhoneBySMSRequest 通过短信验证码更换手机号请求
// type ChangePhoneBySMSRequest struct {
// 	NewPhone   string `json:"new_phone" validate:"required,phone"`
// 	NewSmsCode string `json:"new_sms_code" validate:"required,len=6"`
// }

// // ChangePhoneByWechatRequest 通过微信授权更换手机号请求
// type ChangePhoneByWechatRequest struct {
// 	PhoneCode string `json:"phone_code" validate:"required"`
// }

// DevLoginRequest 开发测试登录请求
type DevLoginRequest struct {
	Phone    string `json:"phone" validate:"required,min=11,max=11"`
	DeviceID string `json:"device_id" validate:"required" comment:"设备唯一标识"`
}

// DevLoginResponse 开发测试登录响应
type DevLoginResponse struct {
	AccessToken string    `json:"access_token"`
	User        *UserInfo `json:"user"`
}
