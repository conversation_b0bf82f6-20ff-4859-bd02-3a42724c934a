package repository

import (
	"context"
	"fmt"
	"strings"
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/internal/types"

	"gorm.io/gorm"
)

type ResumeRepository interface {
	// Resume CRUD operations
	Create(ctx context.Context, resume *model.Resume) error
	GetByID(ctx context.Context, id uint) (*model.Resume, error)
	Update(ctx context.Context, resume *model.Resume) error
	Delete(ctx context.Context, id uint) error
	GetByUserID(ctx context.Context, userID uint) (*model.Resume, error)
	GetList(ctx context.Context, req *types.ResumeQueryRequest) ([]*model.Resume, int64, error)

	// Resume statistics and tracking
	IncrementViewCount(ctx context.Context, id uint) error
	IncrementDownloadCount(ctx context.Context, id uint) error
	UpdateLastActiveTime(ctx context.Context, userID uint) error
	GetResumeStats(ctx context.Context, userID uint) (*model.ResumeStatistics, error)

	// Resume search and filtering for recruiters
	SearchResumes(ctx context.Context, req *types.ResumeQueryRequest) ([]*model.Resume, int64, error)
	GetRecommendedResumes(ctx context.Context, jobID uint, page, pageSize int) ([]*model.Resume, int64, error)
	GetResumesBySkills(ctx context.Context, skills []string, page, pageSize int) ([]*model.Resume, int64, error)
	GetResumesByExperience(ctx context.Context, minExp, maxExp int, page, pageSize int) ([]*model.Resume, int64, error)

	// Resume quality and completion
	GetIncompleteResumes(ctx context.Context, page, pageSize int) ([]*model.Resume, int64, error)
	GetHighQualityResumes(ctx context.Context, page, pageSize int) ([]*model.Resume, int64, error)

	// Resume analytics for users
	GetViewHistory(ctx context.Context, resumeID uint, page, pageSize int) ([]*model.ResumeViewHistory, int64, error)
	RecordResumeView(ctx context.Context, resumeID, viewerID uint, viewerType string) error

	// DB returns the underlying gorm.DB instance
	DB() *gorm.DB
}

type resumeRepository struct {
	db *gorm.DB
}

func NewResumeRepository(db *gorm.DB) ResumeRepository {
	return &resumeRepository{db: db}
}

// DB returns the underlying gorm.DB instance
func (r *resumeRepository) DB() *gorm.DB {
	return r.db
}

// Create creates a new resume
func (r *resumeRepository) Create(ctx context.Context, resume *model.Resume) error {
	return r.db.WithContext(ctx).Create(resume).Error
}

// GetByID retrieves a resume by ID with user information
func (r *resumeRepository) GetByID(ctx context.Context, id uint) (*model.Resume, error) {
	var resume model.Resume
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&resume, id).Error
	return &resume, err
}

// Update updates a resume
func (r *resumeRepository) Update(ctx context.Context, resume *model.Resume) error {
	return r.db.WithContext(ctx).Save(resume).Error
}

// Delete soft deletes a resume
func (r *resumeRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Resume{}, id).Error
}

// GetByUserID retrieves a resume by user ID
func (r *resumeRepository) GetByUserID(ctx context.Context, userID uint) (*model.Resume, error) {
	var resume model.Resume
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Preload("User").
		First(&resume).Error
	return &resume, err
}

// GetList retrieves a paginated list of resumes
func (r *resumeRepository) GetList(ctx context.Context, req *types.ResumeQueryRequest) ([]*model.Resume, int64, error) {
	var resumes []*model.Resume
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Resume{}).Preload("User")

	// Apply filters
	db = r.applyResumeFilters(db, req)

	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	db = r.applyResumeSorting(db, req)

	// Apply pagination
	err := db.Scopes(model.Paginate(req.Page, req.PageSize)).Find(&resumes).Error
	return resumes, total, err
}

// IncrementViewCount increments the view count for a resume
func (r *resumeRepository) IncrementViewCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&model.Resume{}).
		Where("id = ?", id).
		Update("view_count", gorm.Expr("view_count + 1")).Error
}

// IncrementDownloadCount increments the download count for a resume
func (r *resumeRepository) IncrementDownloadCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&model.Resume{}).
		Where("id = ?", id).
		Update("download_count", gorm.Expr("download_count + 1")).Error
}

// UpdateLastActiveTime updates the last active time for a user's resume
func (r *resumeRepository) UpdateLastActiveTime(ctx context.Context, userID uint) error {
	return r.db.WithContext(ctx).Model(&model.Resume{}).
		Where("user_id = ?", userID).
		Update("last_active_at", time.Now()).Error
}

// GetResumeStats retrieves statistics for a user's resume
func (r *resumeRepository) GetResumeStats(ctx context.Context, userID uint) (*model.ResumeStatistics, error) {
	var resume model.Resume
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&resume).Error; err != nil {
		return nil, err
	}

	stats := &model.ResumeStatistics{
		UserID:        userID,
		ResumeID:      resume.ID,
		ViewCount:     int64(resume.ViewCount),
		DownloadCount: int64(resume.DownloadCount),
		LastActiveAt:  resume.LastActiveAt,
	}

	// Count applications submitted
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ?", userID).
		Count(&stats.ApplicationCount)

	// Count applications in last 30 days
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ? AND created_at >= ?", userID, thirtyDaysAgo).
		Count(&stats.RecentApplications)

	// Count favorites received
	r.db.WithContext(ctx).Model(&model.JobFavorite{}).
		Joins("JOIN job_applications ON job_favorites.job_id = job_applications.job_id").
		Where("job_applications.user_id = ?", userID).
		Count(&stats.FavoriteCount)

	return stats, nil
}

// SearchResumes performs advanced resume search for recruiters
func (r *resumeRepository) SearchResumes(ctx context.Context, req *types.ResumeQueryRequest) ([]*model.Resume, int64, error) {
	var resumes []*model.Resume
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Resume{}).Preload("User")

	// Apply search filters
	db = r.applyResumeFilters(db, req)

	// Count total
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	db = r.applyResumeSorting(db, req)

	// Apply pagination
	err := db.Scopes(model.Paginate(req.Page, req.PageSize)).Find(&resumes).Error
	return resumes, total, err
}

// GetRecommendedResumes retrieves recommended resumes for a specific job
func (r *resumeRepository) GetRecommendedResumes(ctx context.Context, jobID uint, page, pageSize int) ([]*model.Resume, int64, error) {
	var resumes []*model.Resume
	var total int64

	// For now, return resumes with complete profiles
	// TODO: Implement recommendation algorithm based on job requirements
	db := r.db.WithContext(ctx).Model(&model.Resume{}).
		Where("name != '' AND phone != '' AND job_intentions IS NOT NULL").
		Preload("User")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("updated_at DESC").
		Find(&resumes).Error
	return resumes, total, err
}

// GetResumesBySkills retrieves resumes containing specific skills
func (r *resumeRepository) GetResumesBySkills(ctx context.Context, skills []string, page, pageSize int) ([]*model.Resume, int64, error) {
	var resumes []*model.Resume
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Resume{}).Preload("User")

	// Search for skills in the skills JSON field
	if len(skills) > 0 {
		for _, skill := range skills {
			db = db.Where("skills::text ILIKE ?", "%"+skill+"%")
		}
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("updated_at DESC").
		Find(&resumes).Error
	return resumes, total, err
}

// GetResumesByExperience retrieves resumes within experience range
func (r *resumeRepository) GetResumesByExperience(ctx context.Context, minExp, maxExp int, page, pageSize int) ([]*model.Resume, int64, error) {
	var resumes []*model.Resume
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Resume{}).Preload("User")

	// Calculate experience based on work_experience JSON field
	// This is a simplified approach - in production, you might want to store calculated experience
	if minExp > 0 || maxExp > 0 {
		// For now, filter based on age as a proxy for experience
		// TODO: Implement proper experience calculation from work_experience JSON
		currentYear := time.Now().Year()
		if minExp > 0 {
			maxAge := currentYear - 1990 - minExp
			db = db.Where("age <= ?", maxAge)
		}
		if maxExp > 0 {
			minAge := currentYear - 1990 - maxExp
			db = db.Where("age >= ?", minAge)
		}
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("updated_at DESC").
		Find(&resumes).Error
	return resumes, total, err
}

// GetIncompleteResumes retrieves resumes that need completion
func (r *resumeRepository) GetIncompleteResumes(ctx context.Context, page, pageSize int) ([]*model.Resume, int64, error) {
	var resumes []*model.Resume
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Resume{}).
		Where("name = '' OR phone = '' OR job_intentions IS NULL OR job_intentions = '[]'").
		Preload("User")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&resumes).Error
	return resumes, total, err
}

// GetHighQualityResumes retrieves high-quality, complete resumes
func (r *resumeRepository) GetHighQualityResumes(ctx context.Context, page, pageSize int) ([]*model.Resume, int64, error) {
	var resumes []*model.Resume
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Resume{}).
		Where("name != '' AND phone != '' AND job_intentions IS NOT NULL AND job_intentions != '[]' AND work_experience IS NOT NULL AND work_experience != '[]'").
		Preload("User")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("updated_at DESC").
		Find(&resumes).Error
	return resumes, total, err
}

// GetViewHistory retrieves view history for a resume
func (r *resumeRepository) GetViewHistory(ctx context.Context, resumeID uint, page, pageSize int) ([]*model.ResumeViewHistory, int64, error) {
	var viewHistory []*model.ResumeViewHistory
	var total int64

	db := r.db.WithContext(ctx).Model(&model.ResumeViewHistory{}).
		Where("resume_id = ?", resumeID)

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("viewed_at DESC").
		Find(&viewHistory).Error
	return viewHistory, total, err
}

// RecordResumeView records a resume view
func (r *resumeRepository) RecordResumeView(ctx context.Context, resumeID, viewerID uint, viewerType string) error {
	viewHistory := &model.ResumeViewHistory{
		ResumeID:   resumeID,
		ViewerID:   viewerID,
		ViewerType: viewerType,
		ViewedAt:   time.Now(),
	}

	return r.db.WithContext(ctx).Create(viewHistory).Error
}

// Helper methods for building queries

// applyResumeFilters applies various filters to the resume query
func (r *resumeRepository) applyResumeFilters(db *gorm.DB, req *types.ResumeQueryRequest) *gorm.DB {
	// Keyword search in name and self introduction
	if req.Keywords != "" {
		keywords := "%" + strings.ToLower(req.Keywords) + "%"
		db = db.Where("LOWER(name) LIKE ? OR LOWER(self_introduction) LIKE ?", keywords, keywords)
	}

	// Age range filter
	if req.MinAge > 0 {
		db = db.Where("age >= ?", req.MinAge)
	}
	if req.MaxAge > 0 {
		db = db.Where("age <= ?", req.MaxAge)
	}

	// Gender filter
	if req.Gender > 0 {
		db = db.Where("gender = ?", req.Gender)
	}

	// Education filter
	if req.Education > 0 {
		// This is simplified - in production, you'd parse education_history JSON
		db = db.Where("education_history::text ILIKE ?", fmt.Sprintf("%%degree%%:%d%%", req.Education))
	}

	// Experience filter
	if req.Experience > 0 {
		// This is simplified - in production, you'd calculate experience from work_experience JSON
		// For now, we'll use a rough age-based approximation
		currentYear := time.Now().Year()
		maxAge := currentYear - 1990 - int(req.Experience)
		db = db.Where("age <= ?", maxAge)
	}

	// Skills filter
	if len(req.Skills) > 0 {
		for _, skill := range req.Skills {
			db = db.Where("skills::text ILIKE ?", "%"+skill+"%")
		}
	}

	// Salary expectation filter
	if req.SalaryMin > 0 {
		db = db.Where("expected_salary_min >= ?", req.SalaryMin)
	}
	if req.SalaryMax > 0 {
		db = db.Where("expected_salary_max <= ?", req.SalaryMax)
	}

	// Work status filter
	if req.WorkStatus != "" {
		db = db.Where("work_status = ?", req.WorkStatus)
	}

	// Location filter
	if len(req.Locations) > 0 {
		for _, location := range req.Locations {
			db = db.Where("preferred_locations::text ILIKE ?", "%"+location+"%")
		}
	}

	return db
}

// applyResumeSorting applies sorting to the resume query
func (r *resumeRepository) applyResumeSorting(db *gorm.DB, req *types.ResumeQueryRequest) *gorm.DB {
	sortBy := req.SortBy
	sortOrder := req.SortOrder

	// Default sorting
	if sortBy == "" {
		sortBy = "updated_at"
	}
	if sortOrder == "" {
		sortOrder = "desc"
	}

	switch sortBy {
	case "created_at":
		db = db.Order(fmt.Sprintf("created_at %s", strings.ToUpper(sortOrder)))
	case "updated_at":
		db = db.Order(fmt.Sprintf("updated_at %s", strings.ToUpper(sortOrder)))
	case "expected_salary_min":
		db = db.Order(fmt.Sprintf("expected_salary_min %s", strings.ToUpper(sortOrder)))
	default:
		db = db.Order("updated_at DESC")
	}

	return db
}
