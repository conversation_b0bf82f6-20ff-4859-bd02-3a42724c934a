package repository

import (
	"context"
	"time"

	"bdb-backend/internal/constants"
	"bdb-backend/internal/model"

	"gorm.io/gorm"
)

type EnterpriseRepository interface {
	// Enterprise CRUD operations
	Create(ctx context.Context, enterprise *model.Enterprise) error
	GetByID(ctx context.Context, id uint) (*model.Enterprise, error)
	Update(ctx context.Context, enterprise *model.Enterprise) error
	Delete(ctx context.Context, id uint) error
	GetByUserID(ctx context.Context, userID uint) (*model.Enterprise, error)
	GetList(ctx context.Context, page, pageSize int) ([]*model.Enterprise, int64, error)

	// Enterprise verification operations
	CreateVerification(ctx context.Context, verification *model.EnterpriseVerification) error
	GetVerificationByEnterpriseID(ctx context.Context, enterpriseID uint) (*model.EnterpriseVerification, error)
	UpdateVerification(ctx context.Context, verification *model.EnterpriseVerification) error
	GetPendingVerifications(ctx context.Context, page, pageSize int) ([]*model.EnterpriseVerification, int64, error)
	ApproveVerification(ctx context.Context, verificationID uint, reviewNote string) error
	RejectVerification(ctx context.Context, verificationID uint, reviewNote string) error

	// Membership operations
	CreateSubscription(ctx context.Context, subscription *model.Subscription) error
	GetActiveSubscriptionByEnterpriseID(ctx context.Context, enterpriseID uint) (*model.Subscription, error)
	UpdateSubscription(ctx context.Context, subscription *model.Subscription) error
	GetExpiredSubscriptions(ctx context.Context) ([]*model.Subscription, error)
	GetMembershipPlans(ctx context.Context) ([]*model.MembershipPlan, error)
	GetMembershipPlanByID(ctx context.Context, id uint) (*model.MembershipPlan, error)

	// Enterprise statistics
	GetEnterpriseStats(ctx context.Context, enterpriseID uint) (*model.EnterpriseStatistics, error)
	IncrementJobPostCount(ctx context.Context, enterpriseID uint) error
	IncrementViewCount(ctx context.Context, enterpriseID uint) error

	// Search and filter
	SearchEnterprises(ctx context.Context, keywords string, page, pageSize int) ([]*model.Enterprise, int64, error)
	GetVerifiedEnterprises(ctx context.Context, page, pageSize int) ([]*model.Enterprise, int64, error)

	// DB returns the underlying gorm.DB instance
	DB() *gorm.DB
}

type enterpriseRepository struct {
	db *gorm.DB
}

func NewEnterpriseRepository(db *gorm.DB) EnterpriseRepository {
	return &enterpriseRepository{db: db}
}

// DB returns the underlying gorm.DB instance
func (r *enterpriseRepository) DB() *gorm.DB {
	return r.db
}

// Create creates a new enterprise
func (r *enterpriseRepository) Create(ctx context.Context, enterprise *model.Enterprise) error {
	return r.db.WithContext(ctx).Create(enterprise).Error
}

// GetByID retrieves an enterprise by ID with verification info
func (r *enterpriseRepository) GetByID(ctx context.Context, id uint) (*model.Enterprise, error) {
	var enterprise model.Enterprise
	err := r.db.WithContext(ctx).
		Preload("Verification").
		Preload("ActiveSubscription").
		First(&enterprise, id).Error
	return &enterprise, err
}

// Update updates an enterprise
func (r *enterpriseRepository) Update(ctx context.Context, enterprise *model.Enterprise) error {
	return r.db.WithContext(ctx).Save(enterprise).Error
}

// Delete soft deletes an enterprise
func (r *enterpriseRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Enterprise{}, id).Error
}

// GetByUserID retrieves an enterprise by user ID
func (r *enterpriseRepository) GetByUserID(ctx context.Context, userID uint) (*model.Enterprise, error) {
	var enterprise model.Enterprise
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Preload("Verification").
		Preload("ActiveSubscription").
		First(&enterprise).Error
	return &enterprise, err
}

// GetList retrieves a paginated list of enterprises
func (r *enterpriseRepository) GetList(ctx context.Context, page, pageSize int) ([]*model.Enterprise, int64, error) {
	var enterprises []*model.Enterprise
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Enterprise{}).
		Preload("Verification")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&enterprises).Error
	return enterprises, total, err
}

// CreateVerification creates a new enterprise verification
func (r *enterpriseRepository) CreateVerification(ctx context.Context, verification *model.EnterpriseVerification) error {
	return r.db.WithContext(ctx).Create(verification).Error
}

// GetVerificationByEnterpriseID retrieves verification info by enterprise ID
func (r *enterpriseRepository) GetVerificationByEnterpriseID(ctx context.Context, enterpriseID uint) (*model.EnterpriseVerification, error) {
	var verification model.EnterpriseVerification
	err := r.db.WithContext(ctx).
		Where("enterprise_id = ?", enterpriseID).
		First(&verification).Error
	return &verification, err
}

// UpdateVerification updates verification info
func (r *enterpriseRepository) UpdateVerification(ctx context.Context, verification *model.EnterpriseVerification) error {
	return r.db.WithContext(ctx).Save(verification).Error
}

// GetPendingVerifications retrieves pending verifications for review
func (r *enterpriseRepository) GetPendingVerifications(ctx context.Context, page, pageSize int) ([]*model.EnterpriseVerification, int64, error) {
	var verifications []*model.EnterpriseVerification
	var total int64

	db := r.db.WithContext(ctx).Model(&model.EnterpriseVerification{}).
		Where("status = ?", constants.VerificationStatusPending).
		Preload("Enterprise")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at ASC").
		Find(&verifications).Error
	return verifications, total, err
}

// ApproveVerification approves an enterprise verification
func (r *enterpriseRepository) ApproveVerification(ctx context.Context, verificationID uint, reviewNote string) error {
	now := time.Now()
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Update verification status
		if err := tx.Model(&model.EnterpriseVerification{}).
			Where("id = ?", verificationID).
			Updates(map[string]interface{}{
				"status":      constants.VerificationStatusApproved,
				"review_note": reviewNote,
				"reviewed_at": &now,
			}).Error; err != nil {
			return err
		}

		// Update enterprise verification status
		var verification model.EnterpriseVerification
		if err := tx.First(&verification, verificationID).Error; err != nil {
			return err
		}

		return tx.Model(&model.Enterprise{}).
			Where("id = ?", verification.EnterpriseID).
			Updates(map[string]interface{}{
				"is_verified":         true,
				"verification_status": constants.VerificationStatusApproved,
			}).Error
	})
}

// RejectVerification rejects an enterprise verification
func (r *enterpriseRepository) RejectVerification(ctx context.Context, verificationID uint, reviewNote string) error {
	now := time.Now()
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Update verification status
		if err := tx.Model(&model.EnterpriseVerification{}).
			Where("id = ?", verificationID).
			Updates(map[string]interface{}{
				"status":      constants.VerificationStatusRejected,
				"review_note": reviewNote,
				"reviewed_at": &now,
			}).Error; err != nil {
			return err
		}

		// Update enterprise verification status
		var verification model.EnterpriseVerification
		if err := tx.First(&verification, verificationID).Error; err != nil {
			return err
		}

		return tx.Model(&model.Enterprise{}).
			Where("id = ?", verification.EnterpriseID).
			Updates(map[string]interface{}{
				"is_verified":         false,
				"verification_status": constants.VerificationStatusRejected,
			}).Error
	})
}

// CreateSubscription creates a new membership subscription
func (r *enterpriseRepository) CreateSubscription(ctx context.Context, subscription *model.Subscription) error {
	return r.db.WithContext(ctx).Create(subscription).Error
}

// GetActiveSubscriptionByEnterpriseID retrieves active subscription for an enterprise
func (r *enterpriseRepository) GetActiveSubscriptionByEnterpriseID(ctx context.Context, enterpriseID uint) (*model.Subscription, error) {
	var subscription model.Subscription
	err := r.db.WithContext(ctx).
		Where("enterprise_id = ? AND status = ? AND end_date > ?",
			enterpriseID, constants.SubscriptionStatusActive, time.Now()).
		Preload("Plan").
		First(&subscription).Error
	return &subscription, err
}

// UpdateSubscription updates a subscription
func (r *enterpriseRepository) UpdateSubscription(ctx context.Context, subscription *model.Subscription) error {
	return r.db.WithContext(ctx).Save(subscription).Error
}

// GetExpiredSubscriptions retrieves expired subscriptions
func (r *enterpriseRepository) GetExpiredSubscriptions(ctx context.Context) ([]*model.Subscription, error) {
	var subscriptions []*model.Subscription
	err := r.db.WithContext(ctx).
		Where("status = ? AND end_date <= ?", constants.SubscriptionStatusActive, time.Now()).
		Find(&subscriptions).Error
	return subscriptions, err
}

// GetMembershipPlans retrieves all active membership plans
func (r *enterpriseRepository) GetMembershipPlans(ctx context.Context) ([]*model.MembershipPlan, error) {
	var plans []*model.MembershipPlan
	err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("sort_order ASC").
		Find(&plans).Error
	return plans, err
}

// GetMembershipPlanByID retrieves a membership plan by ID
func (r *enterpriseRepository) GetMembershipPlanByID(ctx context.Context, id uint) (*model.MembershipPlan, error) {
	var plan model.MembershipPlan
	err := r.db.WithContext(ctx).First(&plan, id).Error
	return &plan, err
}

// GetEnterpriseStats retrieves statistics for an enterprise
func (r *enterpriseRepository) GetEnterpriseStats(ctx context.Context, enterpriseID uint) (*model.EnterpriseStatistics, error) {
	stats := &model.EnterpriseStatistics{
		EnterpriseID: enterpriseID,
	}

	// Count total jobs
	r.db.WithContext(ctx).Model(&model.Job{}).
		Where("enterprise_id = ?", enterpriseID).
		Count(&stats.TotalJobs)

	// Count active jobs
	r.db.WithContext(ctx).Model(&model.Job{}).
		Where("enterprise_id = ? AND status = ?", enterpriseID, constants.JobStatusActive).
		Count(&stats.ActiveJobs)

	// Sum total view count
	r.db.WithContext(ctx).Model(&model.Job{}).
		Where("enterprise_id = ?", enterpriseID).
		Select("COALESCE(SUM(view_count), 0)").
		Scan(&stats.TotalViews)

	// Count total applications
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Joins("JOIN jobs ON job_applications.job_id = jobs.id").
		Where("jobs.enterprise_id = ?", enterpriseID).
		Count(&stats.TotalApplications)

	// Count pending applications
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Joins("JOIN jobs ON job_applications.job_id = jobs.id").
		Where("jobs.enterprise_id = ? AND job_applications.status = ?",
			enterpriseID, constants.ApplicationStatusSubmitted).
		Count(&stats.PendingApplications)

	return stats, nil
}

// IncrementJobPostCount increments the job post count for an enterprise
func (r *enterpriseRepository) IncrementJobPostCount(ctx context.Context, enterpriseID uint) error {
	return r.db.WithContext(ctx).Model(&model.Enterprise{}).
		Where("id = ?", enterpriseID).
		Update("total_job_count", gorm.Expr("total_job_count + 1")).Error
}

// IncrementViewCount increments the view count for an enterprise
func (r *enterpriseRepository) IncrementViewCount(ctx context.Context, enterpriseID uint) error {
	return r.db.WithContext(ctx).Model(&model.Enterprise{}).
		Where("id = ?", enterpriseID).
		Update("total_view_count", gorm.Expr("total_view_count + 1")).Error
}

// SearchEnterprises searches enterprises by keywords
func (r *enterpriseRepository) SearchEnterprises(ctx context.Context, keywords string, page, pageSize int) ([]*model.Enterprise, int64, error) {
	var enterprises []*model.Enterprise
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Enterprise{})

	if keywords != "" {
		searchPattern := "%" + keywords + "%"
		db = db.Where("name ILIKE ? OR description ILIKE ? OR industry ILIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("is_verified DESC, created_at DESC").
		Find(&enterprises).Error
	return enterprises, total, err
}

// GetVerifiedEnterprises retrieves verified enterprises
func (r *enterpriseRepository) GetVerifiedEnterprises(ctx context.Context, page, pageSize int) ([]*model.Enterprise, int64, error) {
	var enterprises []*model.Enterprise
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Enterprise{}).
		Where("is_verified = ?", true)

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&enterprises).Error
	return enterprises, total, err
}
