package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"bdb-backend/internal/constants"
	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type GigService interface {
	CreateGig(ctx context.Context, userID uint, req *types.CreateGigReq) (*model.Gig, error)
	GetGigByID(ctx context.Context, id uint) (*model.Gig, error)
	GetGigs(ctx context.Context, req *types.GigListReq) ([]*model.Gig, int64, error)
	GetMyGigs(ctx context.Context, userID uint, req *types.PaginationReq) ([]*model.Gig, int64, error)
	UpdateGig(ctx context.Context, userID uint, req *types.UpdateGigReq) error
	DeleteGig(ctx context.Context, userID uint, gigID uint) error
	GetUserGigStats(ctx context.Context, userID uint) (*types.UserGigStats, error)

	ApplyForGig(ctx context.Context, userID uint, req *types.ApplyGigReq) (*model.GigApplication, error)
	GetApplicationByID(ctx context.Context, appID uint) (*model.GigApplication, error)
	GetUserApplications(ctx context.Context, userID uint, req *types.PaginationReq) ([]*model.GigApplication, int64, error)
	GetGigApplications(ctx context.Context, gigID uint, req *types.PaginationReq) ([]*model.GigApplication, int64, error)
	ReviewApplication(ctx context.Context, reviewerID uint, req *types.ReviewApplicationReq) error
	UpdateApplicationStatus(ctx context.Context, userID uint, req *types.UpdateApplicationStatusReq) error
	CheckUserApplied(ctx context.Context, gigID uint, userID uint) bool

	// 日历相关
	GetMonthlyStats(ctx context.Context, userID uint, req *types.MonthlyStatsReq) (*types.MonthlyStatsResp, error)
	GetDailyGigs(ctx context.Context, userID uint, req *types.DailyGigReq) (*types.DailyGig, error)
	GetGigsByDateRange(ctx context.Context, userID uint, req *types.DateRangeReq) ([]*model.Gig, error)

	// 定时任务相关
	CheckExpiredGigs(ctx context.Context) error
}

type gigService struct {
	repo     repository.GigRepository
	userRepo repository.UserRepository
}

func NewGigService(
	repo repository.GigRepository,
	userRepo repository.UserRepository,
) GigService {
	return &gigService{
		repo:     repo,
		userRepo: userRepo,
	}
}

func (s *gigService) CreateGig(ctx context.Context, userID uint, req *types.CreateGigReq) (*model.Gig, error) {
	// 验证年龄范围
	if req.AgeMax < req.AgeMin {
		return nil, errors.New("最大年龄不能小于最小年龄")
	}

	startTime, err := time.Parse(time.DateTime, req.StartTime+":00")
	if err != nil {
		return nil, errors.New("invalid start time format")
	}
	endTime, err := time.Parse(time.DateTime, req.EndTime+":00")
	if err != nil {
		return nil, errors.New("invalid end time format")
	}

	gig := &model.Gig{
		UserID:        userID,
		Title:         req.Title,
		Description:   req.Description,
		Salary:        req.Salary,
		SalaryUnit:    req.SalaryUnit,
		Settlement:    req.Settlement,
		PeopleCount:   req.PeopleCount,
		StartTime:     startTime,
		EndTime:       endTime,
		WorkDuration:  int(endTime.Sub(startTime).Minutes()),
		ExpireTime:    endTime.Add(10 * time.Minute),
		AddressName:   req.AddressName,
		Address:       req.Address,
		DetailAddress: req.DetailAddress,
		FullAddress:   req.Address + req.DetailAddress,
		Latitude:      req.Latitude,
		Longitude:     req.Longitude,
		Gender:        req.Gender,
		AgeMin:        req.AgeMin,
		AgeMax:        req.AgeMax,
		Experience:    req.Experience,
		Education:     req.Education,
		Skills:        req.Skills,
		ContactName:   req.ContactName,
		ContactPhone:  req.ContactPhone,
		Status:        constants.GigStatusRecruiting,
		ApprovalMode:  req.ApprovalMode,
		CheckInMethod: req.CheckInMethod,
		IsUrgent:      req.IsUrgent,
		CompanyName:   req.CompanyName,
	}

	tagsJSON, _ := json.Marshal(req.Tags)
	gig.Tags = datatypes.JSON(tagsJSON)
	imagesJSON, _ := json.Marshal(req.Images)
	gig.Images = datatypes.JSON(imagesJSON)

	if err := s.repo.Create(ctx, gig); err != nil {
		return nil, err
	}
	return gig, nil
}

func (s *gigService) GetGigByID(ctx context.Context, id uint) (*model.Gig, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *gigService) GetGigs(ctx context.Context, req *types.GigListReq) ([]*model.Gig, int64, error) {
	return s.repo.GetList(ctx, req)
}

func (s *gigService) DeleteGig(ctx context.Context, userID, gigID uint) error {
	gig, err := s.repo.GetByID(ctx, gigID)
	if err != nil {
		return err
	}
	if gig.UserID != userID {
		return errors.New("user not authorized to delete this gig")
	}
	return s.repo.Delete(ctx, gigID)
}

func (s *gigService) ApplyForGig(ctx context.Context, userID uint, req *types.ApplyGigReq) (*model.GigApplication, error) {
	// 1. Get Gig and perform initial checks
	gig, err := s.repo.GetByID(ctx, req.GigID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("gig not found")
		}
		return nil, err
	}

	if gig.UserID == userID {
		return nil, errors.New("cannot apply to your own gig")
	}

	if gig.Status != constants.GigStatusRecruiting {
		return nil, errors.New("this gig is not open for applications")
	}

	if applied := s.repo.CheckUserApplied(ctx, req.GigID, userID); applied {
		return nil, errors.New("you have already applied to this gig")
	}

	application := &model.GigApplication{
		GigID:                 req.GigID,
		UserID:                userID,
		Message:               req.Message,
		ApplicantName:         req.ApplicantName,
		ApplicantPhone:        req.ApplicantPhone,
		HasExperience:         req.HasExperience,
		ExperienceDescription: req.ExperienceDescription,
	}

	// 2. Handle based on ApprovalMode
	if gig.ApprovalMode == string(constants.GigApprovalModeAuto) {
		// --- AUTO-CONFIRM LOGIC ---
		// 获取 repo 中的 DB 实例来执行事务
		err = s.repo.DB().Transaction(func(tx *gorm.DB) error {
			// Lock the gig row for update to prevent race conditions
			var lockedGig model.Gig
			if err := tx.Set("gorm:query_option", "FOR UPDATE").First(&lockedGig, req.GigID).Error; err != nil {
				return err
			}

			if lockedGig.CurrentPeopleCount >= lockedGig.PeopleCount {
				return errors.New("all spots have been filled")
			}

			// Update counts and status
			lockedGig.CurrentPeopleCount++
			if lockedGig.CurrentPeopleCount == lockedGig.PeopleCount {
				lockedGig.Status = constants.GigStatusLocked
			}

			if err := tx.Save(&lockedGig).Error; err != nil {
				return err
			}

			// Create the application with Confirmed status
			application.Status = constants.GigApplicationStatusConfirmed
			if err := tx.Create(application).Error; err != nil {
				return err
			}

			return nil
		})

		if err != nil {
			return nil, err
		}

	} else {
		// --- MANUAL APPROVAL LOGIC ---
		application.Status = constants.GigApplicationStatusPending
		if err := s.repo.CreateApplication(ctx, application); err != nil {
			return nil, err
		}
	}

	// TODO: Trigger notification
	return application, nil
}

func (s *gigService) GetApplicationByID(ctx context.Context, appID uint) (*model.GigApplication, error) {
	return s.repo.GetApplicationByID(ctx, appID)
}

func (s *gigService) GetUserApplications(ctx context.Context, userID uint, req *types.PaginationReq) ([]*model.GigApplication, int64, error) {
	return s.repo.GetUserApplications(ctx, userID, req.Page, req.PageSize)
}

func (s *gigService) GetGigApplications(ctx context.Context, gigID uint, req *types.PaginationReq) ([]*model.GigApplication, int64, error) {
	return s.repo.GetGigApplications(ctx, gigID, req.Page, req.PageSize)
}

func (s *gigService) ReviewApplication(ctx context.Context, reviewerID uint, req *types.ReviewApplicationReq) error {
	app, err := s.repo.GetApplicationByID(ctx, req.ApplicationID)
	if err != nil {
		return err
	}

	gig, err := s.repo.GetByID(ctx, app.GigID)
	if err != nil {
		return err
	}

	if gig.UserID != reviewerID {
		return errors.New("user is not authorized to review this application")
	}

	app.Status = req.NewStatus
	app.ReviewerNote = req.ReviewerNote
	now := time.Now()
	app.ReviewedAt = &now

	return s.repo.UpdateApplication(ctx, app)
}

func (s *gigService) UpdateApplicationStatus(ctx context.Context, userID uint, req *types.UpdateApplicationStatusReq) error {
	app, err := s.repo.GetApplicationByID(ctx, req.ApplicationID)
	if err != nil {
		return err
	}

	if app.UserID != userID {
		return errors.New("user is not authorized to update this application")
	}

	app.Status = req.NewStatus

	return s.repo.UpdateApplication(ctx, app)
}

// GetMyGigs 获取用户发布的零工列表
func (s *gigService) GetMyGigs(ctx context.Context, userID uint, req *types.PaginationReq) ([]*model.Gig, int64, error) {
	// 获取用户发布的零工列表
	gigs, err := s.repo.GetByUserID(ctx, userID, "")
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户零工列表失败: %w", err)
	}

	// 转换为指针数组
	var gigPtrs []*model.Gig
	for i := range gigs {
		gigPtrs = append(gigPtrs, &gigs[i])
	}

	// 手动分页（这里简化处理，实际应该在数据库层分页）
	total := int64(len(gigPtrs))
	start := (req.Page - 1) * req.PageSize
	end := start + req.PageSize

	if start >= len(gigPtrs) {
		return []*model.Gig{}, total, nil
	}
	if end > len(gigPtrs) {
		end = len(gigPtrs)
	}

	return gigPtrs[start:end], total, nil
}

// UpdateGig 更新零工信息
func (s *gigService) UpdateGig(ctx context.Context, userID uint, req *types.UpdateGigReq) error {
	// 验证年龄范围
	if req.AgeMax < req.AgeMin {
		return errors.New("最大年龄不能小于最小年龄")
	}

	// 首先检查零工是否存在且属于当前用户
	gig, err := s.repo.GetByID(ctx, req.ID)
	if err != nil {
		return fmt.Errorf("获取零工信息失败: %w", err)
	}

	if gig.UserID != userID {
		return errors.New("无权修改此零工")
	}

	// 检查状态是否允许修改
	if gig.Status != constants.GigStatusDraft && gig.Status != constants.GigStatusRecruiting {
		return errors.New("当前状态下无法修改零工信息")
	}

	// 解析时间
	startTime, err := time.Parse(time.RFC3339, req.StartTime)
	if err != nil {
		return fmt.Errorf("开始时间格式错误: %w", err)
	}

	endTime, err := time.Parse(time.RFC3339, req.EndTime)
	if err != nil {
		return fmt.Errorf("结束时间格式错误: %w", err)
	}

	// 更新零工信息
	gig.Title = req.Title
	gig.Description = req.Description
	gig.Salary = req.Salary
	gig.SalaryUnit = req.SalaryUnit
	gig.Settlement = req.Settlement
	gig.PeopleCount = req.PeopleCount
	gig.StartTime = startTime
	gig.EndTime = endTime
	gig.WorkDuration = int(endTime.Sub(startTime).Hours())
	gig.AddressName = req.AddressName
	gig.Address = req.Address
	gig.DetailAddress = req.DetailAddress
	gig.FullAddress = req.Address + " " + req.DetailAddress
	gig.Latitude = req.Latitude
	gig.Longitude = req.Longitude
	gig.Gender = req.Gender
	gig.AgeMin = req.AgeMin
	gig.AgeMax = req.AgeMax
	gig.Experience = req.Experience
	gig.Education = req.Education
	gig.Skills = req.Skills
	gig.ContactName = req.ContactName
	gig.ContactPhone = req.ContactPhone
	gig.IsUrgent = req.IsUrgent
	gig.CompanyName = req.CompanyName
	gig.ApprovalMode = req.ApprovalMode
	gig.CheckInMethod = req.CheckInMethod

	// 转换tags和images为JSON
	if len(req.Tags) > 0 {
		tagsJSON, _ := json.Marshal(req.Tags)
		gig.Tags = datatypes.JSON(tagsJSON)
	}
	if len(req.Images) > 0 {
		imagesJSON, _ := json.Marshal(req.Images)
		gig.Images = datatypes.JSON(imagesJSON)
	}

	// 更新数据库
	if err := s.repo.Update(ctx, gig); err != nil {
		return fmt.Errorf("更新零工信息失败: %w", err)
	}

	return nil
}

// GetUserGigStats 获取用户零工统计信息
func (s *gigService) GetUserGigStats(ctx context.Context, userID uint) (*types.UserGigStats, error) {
	stats := &types.UserGigStats{
		PublishedCount: 0,
		CompletedCount: 0,
		AppliedCount:   0,
		AcceptedCount:  0,
		TotalEarnings:  0,
		AverageRating:  0.0,
	}

	// 获取用户发布的零工统计
	publishedGigs, err := s.repo.GetByUserID(ctx, userID, "")
	if err != nil {
		logger.Error("获取用户发布零工失败", err, "user_id", userID)
	} else {
		stats.PublishedCount = int64(len(publishedGigs))

		// 统计已完成的零工数量和总收入
		for _, gig := range publishedGigs {
			if gig.Status == constants.GigStatusCompleted {
				stats.CompletedCount++
				stats.TotalEarnings += int64(gig.Salary)
			}
		}
	}

	// 获取用户申请的零工统计
	userApps, _, err := s.repo.GetUserApplications(ctx, userID, 1, 1000) // 获取足够多的记录来统计
	if err != nil {
		logger.Error("获取用户申请统计失败", err, "user_id", userID)
	} else {
		stats.AppliedCount = int64(len(userApps))

		// 统计被接受的申请数量
		for _, app := range userApps {
			if app.Status == constants.GigApplicationStatusConfirmed ||
				app.Status == constants.GigApplicationStatusCompleted {
				stats.AcceptedCount++
			}
		}
	}

	// TODO: 实现评分系统后再计算平均评分
	// stats.AverageRating = calculateAverageRating(userID)

	return stats, nil
}

// CheckExpiredGigs 检查并处理过期的零工
func (s *gigService) CheckExpiredGigs(ctx context.Context) error {
	// TODO: 实现此功能，需要仓储层支持
	// 1. 查找所有 end_time < now() 且状态为 "招聘中" 的零工
	// 2. 将这些零工的状态更新为 "已结束"
	logger.Info("定时任务：检查过期零工...")
	return nil
}

// CheckUserApplied 检查用户是否已申请某个零工
func (s *gigService) CheckUserApplied(ctx context.Context, gigID uint, userID uint) bool {
	return s.repo.CheckUserApplied(ctx, gigID, userID)
}

// GetMonthlyStats 获取月度统计信息
func (s *gigService) GetMonthlyStats(ctx context.Context, userID uint, req *types.MonthlyStatsReq) (*types.MonthlyStatsResp, error) {
	// 构造月份的开始和结束时间
	startTime := time.Date(req.Year, time.Month(req.Month), 1, 0, 0, 0, 0, time.UTC)
	endTime := startTime.AddDate(0, 1, 0).Add(-time.Second)

	// 获取用户在该月份的零工数据
	gigs, err := s.repo.GetByUserIDAndDateRange(ctx, userID, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取月度零工数据失败: %w", err)
	}

	// 获取用户在该月份的申请数据
	apps, err := s.repo.GetApplicationsByUserIDAndDateRange(ctx, userID, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取月度申请数据失败: %w", err)
	}

	// 计算统计数据
	totalGigs := int64(len(gigs))
	completedGigs := int64(0)
	totalEarnings := int64(0)

	// 按日期分组统计数据
	dailyStatsMap := make(map[string]*types.DailyGig)

	// 处理零工数据
	for _, gig := range gigs {
		if gig.Status == constants.GigStatusCompleted {
			completedGigs++
			totalEarnings += int64(gig.Salary)
		}

		dateKey := gig.StartTime.Format("2006-01-02")
		if dailyStatsMap[dateKey] == nil {
			dailyStatsMap[dateKey] = &types.DailyGig{
				Date:     dateKey,
				GigCount: 0,
				Earnings: 0,
			}
		}
		dailyStatsMap[dateKey].GigCount++
		if gig.Status == constants.GigStatusCompleted {
			dailyStatsMap[dateKey].Earnings += int64(gig.Salary)
		}
	}

	// 处理申请数据
	for _, app := range apps {
		if app.Status == constants.GigApplicationStatusCompleted {
			// 这里需要获取对应的零工信息来计算收入
			// TODO: 优化查询，避免N+1问题
		}
	}

	// 转换为数组并排序
	var dailyStats []types.DailyGig
	for _, stats := range dailyStatsMap {
		dailyStats = append(dailyStats, *stats)
	}

	return &types.MonthlyStatsResp{
		Year:          req.Year,
		Month:         req.Month,
		TotalGigs:     totalGigs,
		CompletedGigs: completedGigs,
		TotalEarnings: totalEarnings,
		DailyStats:    dailyStats,
	}, nil
}

// GetDailyGigs 获取指定日期的零工数据
func (s *gigService) GetDailyGigs(ctx context.Context, userID uint, req *types.DailyGigReq) (*types.DailyGig, error) {
	// 解析日期
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, fmt.Errorf("日期格式错误: %w", err)
	}

	// 构造一天的开始和结束时间
	startTime := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)
	endTime := startTime.AddDate(0, 0, 1).Add(-time.Second)

	// 获取用户在该日期的零工数据
	gigs, err := s.repo.GetByUserIDAndDateRange(ctx, userID, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取日期零工数据失败: %w", err)
	}

	// 获取用户在该日期的申请数据
	apps, err := s.repo.GetApplicationsByUserIDAndDateRange(ctx, userID, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取日期申请数据失败: %w", err)
	}

	// 计算统计数据
	totalEarnings := int64(0)
	for _, gig := range gigs {
		if gig.Status == constants.GigStatusCompleted {
			totalEarnings += int64(gig.Salary)
		}
	}

	// 转换为响应格式
	var gigDetails []types.GigDetailResp
	for _, gig := range gigs {
		gigDetails = append(gigDetails, s.convertGigToDetailResp(gig))
	}

	var appDetails []types.GigApplicationResp
	for _, app := range apps {
		appDetails = append(appDetails, s.convertApplicationToResp(app))
	}

	return &types.DailyGig{
		Date:     req.Date,
		GigCount: int64(len(gigs)),
		Earnings: totalEarnings,
		Gigs:     gigDetails,
		Apps:     appDetails,
	}, nil
}

// GetGigsByDateRange 获取日期范围内的零工数据
func (s *gigService) GetGigsByDateRange(ctx context.Context, userID uint, req *types.DateRangeReq) ([]*model.Gig, error) {
	// 解析日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, fmt.Errorf("开始日期格式错误: %w", err)
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("结束日期格式错误: %w", err)
	}

	// 构造时间范围
	startTime := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, time.UTC)
	endTime := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 999999999, time.UTC)

	// 获取用户在该时间范围内的零工数据
	gigs, err := s.repo.GetByUserIDAndDateRange(ctx, userID, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取日期范围零工数据失败: %w", err)
	}

	return gigs, nil
}

// convertGigToDetailResp 转换零工模型为详情响应
func (s *gigService) convertGigToDetailResp(gig *model.Gig) types.GigDetailResp {
	// 解析tags
	var tags []string
	if gig.Tags != nil {
		if err := json.Unmarshal(gig.Tags, &tags); err != nil {
			logger.Error("Failed to unmarshal tags", err, "gig_id", gig.ID)
		}
	}

	// 解析images
	var images []string
	if gig.Images != nil {
		if err := json.Unmarshal(gig.Images, &images); err != nil {
			logger.Error("Failed to unmarshal images", err, "gig_id", gig.ID)
		}
	}

	return types.GigDetailResp{
		ID:                 gig.ID,
		UserID:             gig.UserID,
		Title:              gig.Title,
		Description:        gig.Description,
		Salary:             gig.Salary,
		SalaryUnit:         gig.SalaryUnit,
		Settlement:         gig.Settlement,
		PeopleCount:        gig.PeopleCount,
		CurrentPeopleCount: gig.CurrentPeopleCount,
		StartTime:          gig.StartTime,
		EndTime:            gig.EndTime,
		WorkDuration:       gig.WorkDuration,
		AddressName:        gig.AddressName,
		Address:            gig.Address,
		DetailAddress:      gig.DetailAddress,
		FullAddress:        gig.FullAddress,
		Latitude:           gig.Latitude,
		Longitude:          gig.Longitude,
		Gender:             gig.Gender,
		AgeMin:             gig.AgeMin,
		AgeMax:             gig.AgeMax,
		Experience:         gig.Experience,
		Education:          gig.Education,
		Skills:             gig.Skills,
		ContactName:        gig.ContactName,
		ContactPhone:       gig.ContactPhone,
		Status:             gig.Status,
		ApprovalMode:       gig.ApprovalMode,
		CheckInMethod:      gig.CheckInMethod,
		IsUrgent:           gig.IsUrgent,
		CompanyName:        gig.CompanyName,
		Tags:               tags,
		Images:             images,
		CreatedAt:          gig.CreatedAt.Time,
		Publisher:          types.PublisherInfo{}, // TODO: 需要从user表获取
	}
}

// convertApplicationToResp 转换申请模型为响应
func (s *gigService) convertApplicationToResp(app *model.GigApplication) types.GigApplicationResp {
	return types.GigApplicationResp{
		ID:                    app.ID,
		GigID:                 app.GigID,
		UserID:                app.UserID,
		Status:                app.Status,
		Message:               app.Message,
		ApplicantName:         app.ApplicantName,
		ApplicantPhone:        app.ApplicantPhone,
		HasExperience:         app.HasExperience,
		ExperienceDescription: app.ExperienceDescription,
		ReviewedAt:            app.ReviewedAt,
		ReviewerNote:          app.ReviewerNote,
		CheckInAt:             app.CheckInAt,
		CheckOutAt:            app.CheckOutAt,
		CreatedAt:             app.CreatedAt.Time,
		// TODO: 需要根据需要填充 GigInfo 和 ApplicantInfo
	}
}
