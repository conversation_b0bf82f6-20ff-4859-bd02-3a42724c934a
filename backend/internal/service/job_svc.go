package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"bdb-backend/internal/constants"
	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type JobService interface {
	// Job CRUD operations
	CreateJob(ctx context.Context, userID uint, req *types.CreateJobRequest) (*model.Job, error)
	GetJobByID(ctx context.Context, id uint, userID *uint) (*types.JobResponse, error)
	UpdateJob(ctx context.Context, userID uint, jobID uint, req *types.UpdateJobRequest) error
	DeleteJob(ctx context.Context, userID uint, jobID uint) error
	GetJobList(ctx context.Context, req *types.JobQueryRequest) (*types.JobListResponse, error)
	GetMyJobs(ctx context.Context, userID uint, req *types.JobQueryRequest) (*types.JobListResponse, error)

	// Job status management
	RefreshJob(ctx context.Context, userID uint, req *types.JobRefreshRequest) error
	UpdateJobStatus(ctx context.Context, userID uint, req *types.JobStatusUpdateRequest) error
	CloseExpiredJobs(ctx context.Context) error

	// Job search and recommendations
	SearchJobs(ctx context.Context, req *types.JobQueryRequest) (*types.JobListResponse, error)
	GetNearbyJobs(ctx context.Context, lat, lng float64, radius int, page, pageSize int) (*types.JobListResponse, error)
	GetRecommendedJobs(ctx context.Context, userID uint, page, pageSize int) (*types.JobListResponse, error)

	// Job analytics
	GetJobStats(ctx context.Context, userID uint, jobID uint) (*types.StatisticsResponse, error)
	GetUserJobStats(ctx context.Context, userID uint) (*types.StatisticsResponse, error)
	GetJobAnalytics(ctx context.Context, userID uint, jobID uint) (*types.JobAnalyticsResponse, error)

	// Job interactions
	ViewJob(ctx context.Context, jobID uint, userID *uint) error
	GetJobViewers(ctx context.Context, jobID uint, page, pageSize int) ([]*types.UserInfo, int64, error)

	// Batch operations
	BatchUpdateJobStatus(ctx context.Context, userID uint, req *types.BulkActionRequest) error

	// Admin operations
	GetJobsForReview(ctx context.Context, page, pageSize int) (*types.JobListResponse, error)
	ApproveJob(ctx context.Context, jobID uint, note string) error
	RejectJob(ctx context.Context, jobID uint, note string) error
}

type jobService struct {
	jobRepo        repository.JobRepository
	enterpriseRepo repository.EnterpriseRepository
	userRepo       repository.UserRepository
}

func NewJobService(
	jobRepo repository.JobRepository,
	enterpriseRepo repository.EnterpriseRepository,
	userRepo repository.UserRepository,
) JobService {
	return &jobService{
		jobRepo:        jobRepo,
		enterpriseRepo: enterpriseRepo,
		userRepo:       userRepo,
	}
}

// CreateJob creates a new job posting
func (s *jobService) CreateJob(ctx context.Context, userID uint, req *types.CreateJobRequest) (*model.Job, error) {
	// 1. Validate user has an enterprise
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("请先完成企业信息注册")
		}
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	// 2. Check enterprise verification status
	if !enterprise.IsVerified {
		return nil, errors.New("企业未认证，无法发布职位")
	}

	// 3. Check membership limitations
	if err := s.checkJobPostingLimits(ctx, enterprise.ID); err != nil {
		return nil, err
	}

	// 4. Validate job data
	if err := s.validateJobRequest(req); err != nil {
		return nil, err
	}

	// 5. Create job
	job := &model.Job{
		EnterpriseID:      enterprise.ID,
		UserID:            userID,
		Title:             req.Title,
		Description:       req.Description,
		Status:            constants.JobStatusPendingReview,
		SalaryMin:         req.SalaryMin,
		SalaryMax:         req.SalaryMax,
		ExperienceReq:     req.ExperienceReq,
		EducationReq:      req.EducationReq,
		WorkType:          req.WorkType,
		WorkLocation:      req.WorkLocation,
		Latitude:          req.Latitude,
		Longitude:         req.Longitude,
		RemoteWorkSupport: req.RemoteWorkSupport,
		Benefits:          convertStringSliceToJSONJob(req.Benefits),
		JobHighlights:     convertStringSliceToJSONJob(req.JobHighlights),
		Requirements:      convertStringSliceToJSONJob(req.Requirements),
		ContactMethod:     req.ContactMethod,
		IsUrgent:          req.IsUrgent,
		LastRefreshedAt:   time.Now(),
	}

	// Set urgent expiry if urgent
	if req.IsUrgent {
		urgentExpiry := time.Now().AddDate(0, 0, constants.UrgentJobExpirationDays)
		job.UrgentExpiresAt = &urgentExpiry
	}

	if err := s.jobRepo.Create(ctx, job); err != nil {
		return nil, fmt.Errorf("创建职位失败: %w", err)
	}

	// 6. Update enterprise job count
	if err := s.enterpriseRepo.IncrementJobPostCount(ctx, enterprise.ID); err != nil {
		logger.Error("Failed to increment job post count", err, "enterprise_id", enterprise.ID)
	}

	return job, nil
}

// GetJobByID retrieves a job by ID with user context
func (s *jobService) GetJobByID(ctx context.Context, id uint, userID *uint) (*types.JobResponse, error) {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("职位不存在")
		}
		return nil, fmt.Errorf("获取职位失败: %w", err)
	}

	// Convert to response format
	response := s.convertJobToResponse(job, userID)

	return response, nil
}

// UpdateJob updates an existing job
func (s *jobService) UpdateJob(ctx context.Context, userID uint, jobID uint, req *types.UpdateJobRequest) error {
	// 1. Get and validate job ownership
	job, err := s.jobRepo.GetByID(ctx, jobID)
	if err != nil {
		return fmt.Errorf("获取职位失败: %w", err)
	}

	if job.UserID != userID {
		return errors.New("无权修改此职位")
	}

	// 2. Check if job can be updated
	if !s.canUpdateJob(job.Status) {
		return errors.New("当前状态下无法修改职位")
	}

	// 3. Apply updates
	s.applyJobUpdates(job, req)

	// 4. Validate updated data
	if err := s.validateUpdatedJob(job); err != nil {
		return err
	}

	// 5. Update in database
	if err := s.jobRepo.Update(ctx, job); err != nil {
		return fmt.Errorf("更新职位失败: %w", err)
	}

	return nil
}

// DeleteJob deletes a job (soft delete)
func (s *jobService) DeleteJob(ctx context.Context, userID uint, jobID uint) error {
	job, err := s.jobRepo.GetByID(ctx, jobID)
	if err != nil {
		return fmt.Errorf("获取职位失败: %w", err)
	}

	if job.UserID != userID {
		return errors.New("无权删除此职位")
	}

	if !s.canDeleteJob(job.Status) {
		return errors.New("当前状态下无法删除职位")
	}

	return s.jobRepo.Delete(ctx, jobID)
}

// GetJobList retrieves a paginated list of jobs
func (s *jobService) GetJobList(ctx context.Context, req *types.JobQueryRequest) (*types.JobListResponse, error) {
	// Set default values
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	jobs, total, err := s.jobRepo.GetList(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("获取职位列表失败: %w", err)
	}

	// Convert to response format
	jobResponses := make([]types.JobResponse, 0, len(jobs))
	for _, job := range jobs {
		response := s.convertJobToResponse(job, nil)
		jobResponses = append(jobResponses, *response)
	}

	return &types.JobListResponse{
		PaginationResp: types.NewPaginationResp(jobResponses, total, req.Page, req.PageSize),
	}, nil
}

// GetMyJobs retrieves jobs posted by the current user
func (s *jobService) GetMyJobs(ctx context.Context, userID uint, req *types.JobQueryRequest) (*types.JobListResponse, error) {
	jobs, err := s.jobRepo.GetByUserID(ctx, userID, "")
	if err != nil {
		return nil, fmt.Errorf("获取我的职位失败: %w", err)
	}

	// Convert to response format
	jobResponses := make([]types.JobResponse, 0, len(jobs))
	for _, job := range jobs {
		response := s.convertJobToResponse(job, &userID)
		jobResponses = append(jobResponses, *response)
	}

	// Manual pagination for simplicity
	total := int64(len(jobResponses))
	start := (req.Page - 1) * req.PageSize
	end := start + req.PageSize

	if start >= len(jobResponses) {
		jobResponses = []types.JobResponse{}
	} else {
		if end > len(jobResponses) {
			end = len(jobResponses)
		}
		jobResponses = jobResponses[start:end]
	}

	return &types.JobListResponse{
		PaginationResp: types.NewPaginationResp(jobResponses, total, req.Page, req.PageSize),
	}, nil
}

// RefreshJob refreshes a job posting to increase visibility
func (s *jobService) RefreshJob(ctx context.Context, userID uint, req *types.JobRefreshRequest) error {
	job, err := s.jobRepo.GetByID(ctx, req.JobID)
	if err != nil {
		return fmt.Errorf("获取职位失败: %w", err)
	}

	if job.UserID != userID {
		return errors.New("无权刷新此职位")
	}

	// Check if job can be refreshed
	if !constants.CanJobBeRefreshed(job.Status) {
		return errors.New("当前状态下无法刷新职位")
	}

	// Check refresh cooldown
	if time.Since(job.LastRefreshedAt).Hours() < constants.JobRefreshCooldownHours {
		return errors.New("刷新太频繁，请稍后再试")
	}

	// Check daily refresh limit (would need to check membership and count)
	// TODO: Implement membership-based refresh limit checking

	return s.jobRepo.RefreshJob(ctx, req.JobID)
}

// UpdateJobStatus updates the status of a job
func (s *jobService) UpdateJobStatus(ctx context.Context, userID uint, req *types.JobStatusUpdateRequest) error {
	job, err := s.jobRepo.GetByID(ctx, req.JobID)
	if err != nil {
		return fmt.Errorf("获取职位失败: %w", err)
	}

	if job.UserID != userID {
		return errors.New("无权修改此职位状态")
	}

	if !constants.IsValidJobStatus(req.Status) {
		return errors.New("无效的职位状态")
	}

	// Validate status transition
	if !s.canTransitionStatus(job.Status, req.Status) {
		return errors.New("无效的状态转换")
	}

	return s.jobRepo.UpdateStatus(ctx, req.JobID, req.Status)
}

// SearchJobs performs advanced job search
func (s *jobService) SearchJobs(ctx context.Context, req *types.JobQueryRequest) (*types.JobListResponse, error) {
	// Set default values
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	jobs, total, err := s.jobRepo.SearchJobs(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("搜索职位失败: %w", err)
	}

	// Convert to response format
	jobResponses := make([]types.JobResponse, 0, len(jobs))
	for _, job := range jobs {
		response := s.convertJobToResponse(job, nil)
		jobResponses = append(jobResponses, *response)
	}

	return &types.JobListResponse{
		PaginationResp: types.NewPaginationResp(jobResponses, total, req.Page, req.PageSize),
	}, nil
}

// GetNearbyJobs retrieves jobs within a geographic radius
func (s *jobService) GetNearbyJobs(ctx context.Context, lat, lng float64, radius int, page, pageSize int) (*types.JobListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	jobs, total, err := s.jobRepo.GetNearbyJobs(ctx, lat, lng, radius, page, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取附近职位失败: %w", err)
	}

	// Convert to response format
	jobResponses := make([]types.JobResponse, 0, len(jobs))
	for _, job := range jobs {
		response := s.convertJobToResponse(job, nil)
		jobResponses = append(jobResponses, *response)
	}

	return &types.JobListResponse{
		PaginationResp: types.NewPaginationResp(jobResponses, total, page, pageSize),
	}, nil
}

// GetRecommendedJobs retrieves recommended jobs for a user
func (s *jobService) GetRecommendedJobs(ctx context.Context, userID uint, page, pageSize int) (*types.JobListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	jobs, total, err := s.jobRepo.GetRecommendedJobs(ctx, userID, page, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取推荐职位失败: %w", err)
	}

	// Convert to response format
	jobResponses := make([]types.JobResponse, 0, len(jobs))
	for _, job := range jobs {
		response := s.convertJobToResponse(job, &userID)
		jobResponses = append(jobResponses, *response)
	}

	return &types.JobListResponse{
		PaginationResp: types.NewPaginationResp(jobResponses, total, page, pageSize),
	}, nil
}

// ViewJob records a job view and increments view count
func (s *jobService) ViewJob(ctx context.Context, jobID uint, userID *uint) error {
	// Increment view count
	if err := s.jobRepo.IncrementViewCount(ctx, jobID); err != nil {
		logger.Error("Failed to increment view count", err, "job_id", jobID)
	}

	// TODO: Record detailed view history if needed
	// if userID != nil {
	//     s.applicationRepo.CreateViewHistory(ctx, &model.JobViewHistory{
	//         JobID:    jobID,
	//         UserID:   *userID,
	//         ViewTime: time.Now(),
	//     })
	// }

	return nil
}

// CloseExpiredJobs closes jobs that have expired
func (s *jobService) CloseExpiredJobs(ctx context.Context) error {
	logger.Info("Starting expired jobs cleanup...")

	if err := s.jobRepo.CloseExpiredJobs(ctx); err != nil {
		logger.Error("Failed to close expired jobs", err)
		return err
	}

	logger.Info("Expired jobs cleanup completed")
	return nil
}

// Helper methods

// convertStringSliceToJSON converts []string to datatypes.JSON for job service
func convertStringSliceToJSONJob(stringSlice []string) datatypes.JSON {
	if len(stringSlice) == 0 {
		return nil
	}

	jsonData, err := json.Marshal(stringSlice)
	if err != nil {
		// If marshal fails, return nil
		return nil
	}

	return jsonData
}

// convertJSONToStringSlice converts datatypes.JSON to []string for job service
func convertJSONToStringSliceJob(jsonData datatypes.JSON) []string {
	if jsonData == nil {
		return []string{}
	}

	var stringSlice []string
	if err := json.Unmarshal(jsonData, &stringSlice); err != nil {
		// If unmarshal fails, return empty slice
		return []string{}
	}

	return stringSlice
}

// checkJobPostingLimits checks if enterprise can post more jobs based on membership
func (s *jobService) checkJobPostingLimits(ctx context.Context, enterpriseID uint) error {
	// Get active subscription
	subscription, err := s.enterpriseRepo.GetActiveSubscriptionByEnterpriseID(ctx, enterpriseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("请先购买会员套餐")
		}
		return fmt.Errorf("获取会员信息失败: %w", err)
	}

	// Check subscription status
	if subscription.Status != constants.SubscriptionStatusActive {
		return errors.New("会员已过期，请续费")
	}

	// Get current job count for this enterprise
	jobs, err := s.jobRepo.GetByEnterpriseID(ctx, enterpriseID, constants.JobStatusActive)
	if err != nil {
		return fmt.Errorf("获取企业职位数量失败: %w", err)
	}

	// TODO: Check against membership plan limits
	// This would require parsing the subscription plan benefits
	// For now, we'll use a simple check
	if len(jobs) >= 50 { // Default limit
		return errors.New("已达到职位发布数量上限")
	}

	return nil
}

// validateJobRequest validates job creation request
func (s *jobService) validateJobRequest(req *types.CreateJobRequest) error {
	if len(req.Title) < 2 || len(req.Title) > constants.MaxJobTitleLength {
		return errors.New("职位标题长度应在2-50字符之间")
	}

	if len(req.Description) < 10 || len(req.Description) > constants.MaxJobDescriptionLength {
		return errors.New("职位描述长度应在10-5000字符之间")
	}

	if req.SalaryMin < 0 || req.SalaryMax < 0 {
		return errors.New("薪资不能为负数")
	}

	if req.SalaryMax > 0 && req.SalaryMax < req.SalaryMin {
		return errors.New("最高薪资不能低于最低薪资")
	}

	if !constants.IsValidWorkType(req.WorkType) {
		return errors.New("无效的工作类型")
	}

	if !constants.IsValidContactMethod(req.ContactMethod) {
		return errors.New("无效的联系方式")
	}

	if len(req.JobHighlights) > constants.MaxJobHighlightsCount {
		return errors.New("职位亮点数量过多")
	}

	if len(req.Requirements) > constants.MaxRequirementsCount {
		return errors.New("任职要求数量过多")
	}

	if len(req.Benefits) > constants.MaxBenefitsCount {
		return errors.New("福利待遇数量过多")
	}

	return nil
}

// canUpdateJob checks if a job can be updated based on its status
func (s *jobService) canUpdateJob(status string) bool {
	return status == constants.JobStatusPendingReview ||
		status == constants.JobStatusActive ||
		status == constants.JobStatusPaused
}

// canDeleteJob checks if a job can be deleted based on its status
func (s *jobService) canDeleteJob(status string) bool {
	return status != constants.JobStatusClosed && status != constants.JobStatusExpired
}

// canTransitionStatus checks if status transition is valid
func (s *jobService) canTransitionStatus(from, to string) bool {
	// Define valid status transitions
	transitions := map[string][]string{
		constants.JobStatusPendingReview: {constants.JobStatusActive, constants.JobStatusRejected},
		constants.JobStatusActive:        {constants.JobStatusPaused, constants.JobStatusClosed},
		constants.JobStatusPaused:        {constants.JobStatusActive, constants.JobStatusClosed},
		constants.JobStatusRejected:      {constants.JobStatusPendingReview},
	}

	validTransitions, exists := transitions[from]
	if !exists {
		return false
	}

	for _, validTo := range validTransitions {
		if to == validTo {
			return true
		}
	}

	return false
}

// applyJobUpdates applies updates to a job model
func (s *jobService) applyJobUpdates(job *model.Job, req *types.UpdateJobRequest) {
	if req.Title != nil {
		job.Title = *req.Title
	}
	if req.Description != nil {
		job.Description = *req.Description
	}
	if req.SalaryMin != nil {
		job.SalaryMin = *req.SalaryMin
	}
	if req.SalaryMax != nil {
		job.SalaryMax = *req.SalaryMax
	}
	if req.ExperienceReq != nil {
		job.ExperienceReq = *req.ExperienceReq
	}
	if req.EducationReq != nil {
		job.EducationReq = *req.EducationReq
	}
	if req.WorkType != nil {
		job.WorkType = *req.WorkType
	}
	if req.WorkLocation != nil {
		job.WorkLocation = *req.WorkLocation
	}
	if req.Latitude != nil {
		job.Latitude = *req.Latitude
	}
	if req.Longitude != nil {
		job.Longitude = *req.Longitude
	}
	if req.RemoteWorkSupport != nil {
		job.RemoteWorkSupport = *req.RemoteWorkSupport
	}
	if req.Benefits != nil {
		job.Benefits = convertStringSliceToJSONJob(req.Benefits)
	}
	if req.JobHighlights != nil {
		job.JobHighlights = convertStringSliceToJSONJob(req.JobHighlights)
	}
	if req.Requirements != nil {
		job.Requirements = convertStringSliceToJSONJob(req.Requirements)
	}
	if req.ContactMethod != nil {
		job.ContactMethod = *req.ContactMethod
	}
	if req.IsUrgent != nil {
		job.IsUrgent = *req.IsUrgent
		if *req.IsUrgent && job.UrgentExpiresAt == nil {
			urgentExpiry := time.Now().AddDate(0, 0, constants.UrgentJobExpirationDays)
			job.UrgentExpiresAt = &urgentExpiry
		} else if !*req.IsUrgent {
			job.UrgentExpiresAt = nil
		}
	}
}

// validateUpdatedJob validates job after updates
func (s *jobService) validateUpdatedJob(job *model.Job) error {
	if len(job.Title) < 2 || len(job.Title) > constants.MaxJobTitleLength {
		return errors.New("职位标题长度应在2-50字符之间")
	}

	if len(job.Description) < 10 || len(job.Description) > constants.MaxJobDescriptionLength {
		return errors.New("职位描述长度应在10-5000字符之间")
	}

	if job.SalaryMax > 0 && job.SalaryMax < job.SalaryMin {
		return errors.New("最高薪资不能低于最低薪资")
	}

	return nil
}

// convertJobToResponse converts a job model to response format
func (s *jobService) convertJobToResponse(job *model.Job, userID *uint) *types.JobResponse {
	response := &types.JobResponse{
		ID:                 job.ID,
		EnterpriseID:       job.EnterpriseID,
		UserID:             job.UserID,
		Title:              job.Title,
		Description:        job.Description,
		Status:             job.Status,
		StatusLabel:        "", // Label should be computed on frontend
		StatusColor:        "", // Color should be determined on frontend
		SalaryMin:          job.SalaryMin,
		SalaryMax:          job.SalaryMax,
		ExperienceReq:      job.ExperienceReq,
		ExperienceLabel:    "", // Label should be computed on frontend
		EducationReq:       job.EducationReq,
		EducationLabel:     "", // Label should be computed on frontend
		WorkType:           job.WorkType,
		WorkTypeLabel:      "", // Label should be computed on frontend
		WorkLocation:       job.WorkLocation,
		Latitude:           job.Latitude,
		Longitude:          job.Longitude,
		RemoteWorkSupport:  job.RemoteWorkSupport,
		Benefits:           convertJSONToStringSliceJob(job.Benefits),
		JobHighlights:      convertJSONToStringSliceJob(job.JobHighlights),
		Requirements:       convertJSONToStringSliceJob(job.Requirements),
		ContactMethod:      job.ContactMethod,
		ContactMethodLabel: constants.GetContactMethodLabel(job.ContactMethod),
		IsUrgent:           job.IsUrgent,
		UrgentExpiresAt:    job.UrgentExpiresAt,
		LastRefreshedAt:    job.LastRefreshedAt,
		TodayRefreshCount:  job.TodayRefreshCount,
		ViewCount:          job.ViewCount,
		ApplicationCount:   job.ApplicationCount,
		CreatedAt:          job.CreatedAt,
		UpdatedAt:          job.UpdatedAt,
	}

	// Generate salary text
	if job.SalaryMin > 0 && job.SalaryMax > 0 {
		response.SalaryText = fmt.Sprintf("%d-%d元/月", job.SalaryMin, job.SalaryMax)
	} else if job.SalaryMin > 0 {
		response.SalaryText = fmt.Sprintf("%d元以上/月", job.SalaryMin)
	} else {
		response.SalaryText = "面议"
	}

	// Add enterprise info if available
	if job.Enterprise != nil {
		response.Enterprise = &types.EnterpriseInfo{
			ID:                 job.Enterprise.ID,
			UserID:             job.Enterprise.UserID,
			Name:               job.Enterprise.Name,
			Description:        job.Enterprise.Description,
			LogoURL:            job.Enterprise.LogoURL,
			Type:               job.Enterprise.Type,
			TypeLabel:          job.Enterprise.Type,
			Industry:           job.Enterprise.Industry,
			CompanySize:        job.Enterprise.CompanySize,
			ContactPerson:      job.Enterprise.ContactPerson,
			ContactPhone:       job.Enterprise.ContactPhone,
			Address:            job.Enterprise.Address,
			Latitude:           job.Enterprise.Latitude,
			Longitude:          job.Enterprise.Longitude,
			WelfareTags:        convertJSONToStringSliceJob(job.Enterprise.WelfareTags),
			IsVerified:         job.Enterprise.IsVerified,
			VerificationStatus: job.Enterprise.VerificationStatus,
			ActiveJobCount:     job.Enterprise.ActiveJobCount,
			TotalViewCount:     job.Enterprise.TotalViewCount,
			CreatedAt:          job.Enterprise.CreatedAt,
			UpdatedAt:          job.Enterprise.UpdatedAt,
		}
	}

	// Set user-specific fields if user ID provided
	if userID != nil {
		// TODO: Check if user has applied or favorited this job
		// response.HasApplied = s.applicationRepo.CheckUserApplied(ctx, *userID, job.ID)
		// response.IsFavorited = s.applicationRepo.CheckIsFavorited(ctx, *userID, job.ID)
	}

	return response
}

// GetJobStats retrieves statistics for a specific job
func (s *jobService) GetJobStats(ctx context.Context, userID uint, jobID uint) (*types.StatisticsResponse, error) {
	// Verify job ownership
	job, err := s.jobRepo.GetByID(ctx, jobID)
	if err != nil {
		return nil, fmt.Errorf("获取职位失败: %w", err)
	}

	if job.UserID != userID {
		return nil, errors.New("无权查看此职位统计")
	}

	stats, err := s.jobRepo.GetJobStats(ctx, jobID)
	if err != nil {
		return nil, fmt.Errorf("获取职位统计失败: %w", err)
	}

	return &types.StatisticsResponse{
		TotalJobs:         1,
		ActiveJobs:        1,
		TotalApplications: int(stats.ApplicationCount),
		TotalViews:        int(stats.ViewCount),
		TotalFavorites:    int(stats.FavoriteCount),
	}, nil
}

// GetUserJobStats retrieves aggregated statistics for all user's jobs
func (s *jobService) GetUserJobStats(ctx context.Context, userID uint) (*types.StatisticsResponse, error) {
	// Get user's enterprise
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	stats, err := s.enterpriseRepo.GetEnterpriseStats(ctx, enterprise.ID)
	if err != nil {
		return nil, fmt.Errorf("获取统计信息失败: %w", err)
	}

	return &types.StatisticsResponse{
		TotalJobs:           int(stats.TotalJobs),
		ActiveJobs:          int(stats.ActiveJobs),
		TotalApplications:   int(stats.TotalApplications),
		PendingApplications: int(stats.PendingApplications),
		TotalViews:          int(stats.TotalViews),
	}, nil
}

// GetJobAnalytics retrieves detailed analytics for a job
func (s *jobService) GetJobAnalytics(ctx context.Context, userID uint, jobID uint) (*types.JobAnalyticsResponse, error) {
	// Verify job ownership
	job, err := s.jobRepo.GetByID(ctx, jobID)
	if err != nil {
		return nil, fmt.Errorf("获取职位失败: %w", err)
	}

	if job.UserID != userID {
		return nil, errors.New("无权查看此职位分析")
	}

	// TODO: Implement detailed analytics
	// This would include conversion rates, popular keywords, salary comparisons, etc.

	return &types.JobAnalyticsResponse{
		JobID:              jobID,
		Views:              job.ViewCount,
		Applications:       job.ApplicationCount,
		ConversionRate:     0.0,        // TODO: Calculate
		AvgResponseTime:    0,          // TODO: Calculate
		PopularKeywords:    []string{}, // TODO: Analyze
		SimilarJobsSalary:  [2]int{job.SalaryMin, job.SalaryMax},
		RecommendedActions: []string{}, // TODO: Generate recommendations
	}, nil
}

// GetJobsForReview retrieves jobs pending admin review
func (s *jobService) GetJobsForReview(ctx context.Context, page, pageSize int) (*types.JobListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	jobs, total, err := s.jobRepo.GetJobsForReview(ctx, page, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取待审核职位失败: %w", err)
	}

	// Convert to response format
	jobResponses := make([]types.JobResponse, 0, len(jobs))
	for _, job := range jobs {
		response := s.convertJobToResponse(job, nil)
		jobResponses = append(jobResponses, *response)
	}

	return &types.JobListResponse{
		PaginationResp: types.NewPaginationResp(jobResponses, total, page, pageSize),
	}, nil
}

// ApproveJob approves a job posting
func (s *jobService) ApproveJob(ctx context.Context, jobID uint, note string) error {
	return s.jobRepo.UpdateStatus(ctx, jobID, constants.JobStatusActive)
}

// RejectJob rejects a job posting
func (s *jobService) RejectJob(ctx context.Context, jobID uint, note string) error {
	return s.jobRepo.UpdateStatus(ctx, jobID, constants.JobStatusRejected)
}

// BatchUpdateJobStatus updates status for multiple jobs
func (s *jobService) BatchUpdateJobStatus(ctx context.Context, userID uint, req *types.BulkActionRequest) error {
	// Verify all jobs belong to the user
	for _, id := range req.IDs {
		job, err := s.jobRepo.GetByID(ctx, id)
		if err != nil {
			return fmt.Errorf("获取职位 %d 失败: %w", id, err)
		}

		if job.UserID != userID {
			return fmt.Errorf("无权操作职位 %d", id)
		}
	}

	// Perform batch update based on action
	switch req.Action {
	case "pause":
		return s.jobRepo.BatchUpdateStatus(ctx, req.IDs, constants.JobStatusPaused)
	case "activate":
		return s.jobRepo.BatchUpdateStatus(ctx, req.IDs, constants.JobStatusActive)
	case "close":
		return s.jobRepo.BatchUpdateStatus(ctx, req.IDs, constants.JobStatusClosed)
	default:
		return errors.New("无效的批量操作")
	}
}

// GetJobViewers retrieves users who viewed a job (placeholder)
func (s *jobService) GetJobViewers(ctx context.Context, jobID uint, page, pageSize int) ([]*types.UserInfo, int64, error) {
	// TODO: Implement job viewer tracking
	return []*types.UserInfo{}, 0, nil
}
