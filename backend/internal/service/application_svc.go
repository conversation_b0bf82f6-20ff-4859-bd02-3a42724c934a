package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"bdb-backend/internal/constants"
	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type ApplicationService interface {
	// Job application operations
	ApplyForJob(ctx context.Context, userID uint, req *types.CreateApplicationRequest) (*model.JobApplication, error)
	GetApplicationByID(ctx context.Context, id uint, userID uint) (*types.ApplicationResponse, error)
	GetMyApplications(ctx context.Context, userID uint, req *types.ApplicationQueryRequest) (*types.ApplicationListResponse, error)
	CancelApplication(ctx context.Context, userID uint, applicationID uint) error

	// Recruiter application management
	GetJobApplications(ctx context.Context, userID uint, jobID uint, req *types.ApplicationQueryRequest) (*types.ApplicationListResponse, error)
	GetEnterpriseApplications(ctx context.Context, userID uint, req *types.ApplicationQueryRequest) (*types.ApplicationListResponse, error)
	UpdateApplicationStatus(ctx context.Context, userID uint, req *types.UpdateApplicationStatusRequest) error
	ScheduleInterview(ctx context.Context, userID uint, applicationID uint, interviewTime time.Time, address string) error

	// Application analytics
	GetApplicationStats(ctx context.Context, userID uint, jobID *uint) (*types.StatisticsResponse, error)
	GetApplicationTrends(ctx context.Context, userID uint, days int) ([]*model.ApplicationTrend, error)

	// Batch operations
	BatchUpdateApplicationStatus(ctx context.Context, userID uint, req *types.BulkActionRequest) error
	BatchRejectApplications(ctx context.Context, userID uint, jobID uint, reason string) error

	// Notification management
	GetNotifications(ctx context.Context, userID uint, page, pageSize int) ([]*types.NotificationResponse, error)
	MarkNotificationAsRead(ctx context.Context, userID uint, notificationID uint) error
	MarkAllNotificationsAsRead(ctx context.Context, userID uint) error

	// Favorites management
	AddJobToFavorites(ctx context.Context, userID uint, req *types.CreateFavoriteRequest) error
	RemoveJobFromFavorites(ctx context.Context, userID uint, jobID uint) error
	GetMyFavorites(ctx context.Context, userID uint, page, pageSize int) ([]*types.FavoriteResponse, int64, error)
	CheckJobFavorited(ctx context.Context, userID uint, jobID uint) (bool, error)

	// View history
	GetMyViewHistory(ctx context.Context, userID uint, page, pageSize int) ([]*types.ViewHistoryResponse, int64, error)
	ClearViewHistory(ctx context.Context, userID uint) error

	// Reporting and moderation
	ReportJob(ctx context.Context, userID uint, jobID uint, reason, description string) error
	GetMyReports(ctx context.Context, userID uint, page, pageSize int) ([]*model.JobReport, int64, error)

	// Admin operations
	GetReportsByStatus(ctx context.Context, status string, page, pageSize int) ([]*model.JobReport, int64, error)
	HandleReport(ctx context.Context, reportID uint, status, response string) error

	// Communication
	SendMessageToRecruiter(ctx context.Context, userID uint, jobID uint, message string) error
	SendBulkNotification(ctx context.Context, userIDs []uint, title, content, notificationType string) error
}

type applicationService struct {
	applicationRepo repository.ApplicationRepository
	jobRepo         repository.JobRepository
	resumeRepo      repository.ResumeRepository
	enterpriseRepo  repository.EnterpriseRepository
	userRepo        repository.UserRepository
}

func NewApplicationService(
	applicationRepo repository.ApplicationRepository,
	jobRepo repository.JobRepository,
	resumeRepo repository.ResumeRepository,
	enterpriseRepo repository.EnterpriseRepository,
	userRepo repository.UserRepository,
) ApplicationService {
	return &applicationService{
		applicationRepo: applicationRepo,
		jobRepo:         jobRepo,
		resumeRepo:      resumeRepo,
		enterpriseRepo:  enterpriseRepo,
		userRepo:        userRepo,
	}
}

// ApplyForJob submits a job application
func (s *applicationService) ApplyForJob(ctx context.Context, userID uint, req *types.CreateApplicationRequest) (*model.JobApplication, error) {
	// 1. Get and validate job
	job, err := s.jobRepo.GetByID(ctx, req.JobID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("职位不存在")
		}
		return nil, fmt.Errorf("获取职位失败: %w", err)
	}

	// 2. Check job status
	if !constants.CanApplyToJob(job.Status) {
		return nil, errors.New("该职位当前不接受申请")
	}

	// 3. Check if user owns the job
	if job.UserID == userID {
		return nil, errors.New("不能申请自己发布的职位")
	}

	// 4. Check if user already applied
	if s.applicationRepo.CheckUserApplied(ctx, userID, req.JobID) {
		return nil, errors.New("您已申请过此职位")
	}

	// 5. Validate user's resume
	resume, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("请先完善简历信息")
		}
		return nil, fmt.Errorf("获取简历失败: %w", err)
	}

	if !resume.CanApplyJobs() {
		return nil, errors.New("简历信息不完整，请完善必要信息后再申请")
	}

	// 6. Check daily application limit
	if err := s.checkApplicationLimits(ctx, userID); err != nil {
		return nil, err
	}

	// 7. Create resume snapshot for application
	resumeSnapshot, err := s.createResumeSnapshot(resume)
	if err != nil {
		return nil, fmt.Errorf("创建简历快照失败: %w", err)
	}

	// 8. Create application
	application := &model.JobApplication{
		JobID:          req.JobID,
		UserID:         userID,
		Status:         constants.ApplicationStatusSubmitted,
		ResumeSnapshot: resumeSnapshot,
	}

	if err := s.applicationRepo.Create(ctx, application); err != nil {
		return nil, fmt.Errorf("提交申请失败: %w", err)
	}

	// 9. Send notification to recruiter
	if err := s.sendApplicationNotification(ctx, application, job); err != nil {
		logger.Error("Failed to send application notification", err, "application_id", application.ID)
	}

	return application, nil
}

// GetApplicationByID retrieves an application by ID
func (s *applicationService) GetApplicationByID(ctx context.Context, id uint, userID uint) (*types.ApplicationResponse, error) {
	application, err := s.applicationRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("申请不存在")
		}
		return nil, fmt.Errorf("获取申请失败: %w", err)
	}

	// Check access permissions
	if application.UserID != userID && application.Job.UserID != userID {
		return nil, errors.New("无权查看此申请")
	}

	return s.convertApplicationToResponse(application), nil
}

// GetMyApplications retrieves current user's applications
func (s *applicationService) GetMyApplications(ctx context.Context, userID uint, req *types.ApplicationQueryRequest) (*types.ApplicationListResponse, error) {
	// Set default values
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	req.UserID = userID // Ensure filtering by current user

	applications, total, err := s.applicationRepo.GetList(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("获取申请列表失败: %w", err)
	}

	// Convert to response format
	applicationResponses := make([]types.ApplicationResponse, 0, len(applications))
	for _, application := range applications {
		response := s.convertApplicationToResponse(application)
		applicationResponses = append(applicationResponses, *response)
	}

	return &types.ApplicationListResponse{
		PaginationResp: types.NewPaginationResp(applicationResponses, total, req.Page, req.PageSize),
	}, nil
}

// GetJobApplications retrieves applications for a specific job (recruiter view)
func (s *applicationService) GetJobApplications(ctx context.Context, userID uint, jobID uint, req *types.ApplicationQueryRequest) (*types.ApplicationListResponse, error) {
	// Verify job ownership
	job, err := s.jobRepo.GetByID(ctx, jobID)
	if err != nil {
		return nil, fmt.Errorf("获取职位失败: %w", err)
	}

	if job.UserID != userID {
		return nil, errors.New("无权查看此职位的申请")
	}

	// Set default values
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	req.JobID = jobID

	applications, total, err := s.applicationRepo.GetList(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("获取申请列表失败: %w", err)
	}

	// Convert to response format
	applicationResponses := make([]types.ApplicationResponse, 0, len(applications))
	for _, application := range applications {
		response := s.convertApplicationToResponse(application)
		applicationResponses = append(applicationResponses, *response)
	}

	return &types.ApplicationListResponse{
		PaginationResp: types.NewPaginationResp(applicationResponses, total, req.Page, req.PageSize),
	}, nil
}

// UpdateApplicationStatus updates the status of an application (recruiter action)
func (s *applicationService) UpdateApplicationStatus(ctx context.Context, userID uint, req *types.UpdateApplicationStatusRequest) error {
	// Get application with job info
	application, err := s.applicationRepo.GetByID(ctx, req.ApplicationID)
	if err != nil {
		return fmt.Errorf("获取申请失败: %w", err)
	}

	// Verify job ownership
	if application.Job.UserID != userID {
		return errors.New("无权操作此申请")
	}

	// Validate status transition
	if !constants.IsValidApplicationStatus(req.Status) {
		return errors.New("无效的申请状态")
	}

	// Update application status
	if err := s.applicationRepo.UpdateStatus(ctx, req.ApplicationID, req.Status, req.RecruiterNote); err != nil {
		return fmt.Errorf("更新申请状态失败: %w", err)
	}

	// Send notification to applicant
	if err := s.sendStatusUpdateNotification(ctx, application, req.Status, req.RecruiterNote); err != nil {
		logger.Error("Failed to send status update notification", err, "application_id", req.ApplicationID)
	}

	return nil
}

// ScheduleInterview schedules an interview for an application
func (s *applicationService) ScheduleInterview(ctx context.Context, userID uint, applicationID uint, interviewTime time.Time, address string) error {
	// Get application with job info
	application, err := s.applicationRepo.GetByID(ctx, applicationID)
	if err != nil {
		return fmt.Errorf("获取申请失败: %w", err)
	}

	// Verify job ownership
	if application.Job.UserID != userID {
		return errors.New("无权操作此申请")
	}

	// Validate interview time (must be in the future)
	if interviewTime.Before(time.Now()) {
		return errors.New("面试时间不能是过去的时间")
	}

	// Schedule interview
	if err := s.applicationRepo.ScheduleInterview(ctx, applicationID, interviewTime, address); err != nil {
		return fmt.Errorf("安排面试失败: %w", err)
	}

	// Send interview notification
	if err := s.sendInterviewNotification(ctx, application, interviewTime, address); err != nil {
		logger.Error("Failed to send interview notification", err, "application_id", applicationID)
	}

	return nil
}

// AddJobToFavorites adds a job to user's favorites
func (s *applicationService) AddJobToFavorites(ctx context.Context, userID uint, req *types.CreateFavoriteRequest) error {
	// Check if job exists
	_, err := s.jobRepo.GetByID(ctx, req.JobID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("职位不存在")
		}
		return fmt.Errorf("获取职位失败: %w", err)
	}

	// Check if already favorited
	if s.applicationRepo.CheckIsFavorited(ctx, userID, req.JobID) {
		return errors.New("已收藏此职位")
	}

	// Create favorite
	favorite := &model.JobFavorite{
		UserID: userID,
		JobID:  req.JobID,
	}

	return s.applicationRepo.CreateFavorite(ctx, favorite)
}

// RemoveJobFromFavorites removes a job from user's favorites
func (s *applicationService) RemoveJobFromFavorites(ctx context.Context, userID uint, jobID uint) error {
	return s.applicationRepo.RemoveFavorite(ctx, userID, jobID)
}

// GetMyFavorites retrieves user's favorite jobs
func (s *applicationService) GetMyFavorites(ctx context.Context, userID uint, page, pageSize int) ([]*types.FavoriteResponse, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	favorites, total, err := s.applicationRepo.GetUserFavorites(ctx, userID, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取收藏列表失败: %w", err)
	}

	// Convert to response format
	result := make([]*types.FavoriteResponse, 0, len(favorites))
	for _, favorite := range favorites {
		response := s.convertFavoriteToResponse(favorite)
		result = append(result, response)
	}

	return result, total, nil
}

// CheckJobFavorited checks if a job is favorited by user
func (s *applicationService) CheckJobFavorited(ctx context.Context, userID uint, jobID uint) (bool, error) {
	return s.applicationRepo.CheckIsFavorited(ctx, userID, jobID), nil
}

// GetNotifications retrieves user's notifications
func (s *applicationService) GetNotifications(ctx context.Context, userID uint, page, pageSize int) ([]*types.NotificationResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	notifications, err := s.applicationRepo.GetPendingNotifications(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取通知失败: %w", err)
	}

	// Convert to response format
	result := make([]*types.NotificationResponse, 0, len(notifications))
	for _, notification := range notifications {
		response := s.convertNotificationToResponse(notification)
		result = append(result, response)
	}

	return result, nil
}

// MarkNotificationAsRead marks a notification as read
func (s *applicationService) MarkNotificationAsRead(ctx context.Context, userID uint, notificationID uint) error {
	// TODO: Add user permission check
	return s.applicationRepo.MarkNotificationAsRead(ctx, notificationID)
}

// ReportJob reports a job for inappropriate content
func (s *applicationService) ReportJob(ctx context.Context, userID uint, jobID uint, reason, description string) error {
	// Check if job exists
	_, err := s.jobRepo.GetByID(ctx, jobID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("职位不存在")
		}
		return fmt.Errorf("获取职位失败: %w", err)
	}

	// Create report
	report := &model.JobReport{
		ReportedJobID:  &jobID,
		ReporterUserID: userID,
		Reason:         reason,
		Description:    description,
		Status:         constants.ReportStatusPending,
	}

	return s.applicationRepo.CreateReport(ctx, report)
}

// GetApplicationStats retrieves application statistics
func (s *applicationService) GetApplicationStats(ctx context.Context, userID uint, jobID *uint) (*types.StatisticsResponse, error) {
	if jobID != nil {
		// Get stats for specific job
		job, err := s.jobRepo.GetByID(ctx, *jobID)
		if err != nil {
			return nil, fmt.Errorf("获取职位失败: %w", err)
		}

		if job.UserID != userID {
			return nil, errors.New("无权查看此职位统计")
		}

		stats, err := s.applicationRepo.GetApplicationStats(ctx, *jobID)
		if err != nil {
			return nil, fmt.Errorf("获取申请统计失败: %w", err)
		}

		return &types.StatisticsResponse{
			TotalApplications:   int(stats.TotalApplications),
			PendingApplications: int(stats.PendingApplications),
		}, nil
	} else {
		// Get stats for all user's jobs
		enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
		if err != nil {
			return nil, fmt.Errorf("获取企业信息失败: %w", err)
		}

		stats, err := s.applicationRepo.GetEnterpriseApplicationStats(ctx, enterprise.ID)
		if err != nil {
			return nil, fmt.Errorf("获取申请统计失败: %w", err)
		}

		return &types.StatisticsResponse{
			TotalApplications:   int(stats.TotalApplications),
			PendingApplications: int(stats.PendingApplications),
		}, nil
	}
}

// Helper methods

// convertJSONToStringSlice converts datatypes.JSON to []string
func convertJSONToStringSlice(jsonData datatypes.JSON) []string {
	if jsonData == nil {
		return []string{}
	}

	var stringSlice []string
	if err := json.Unmarshal(jsonData, &stringSlice); err != nil {
		// If unmarshal fails, return empty slice
		return []string{}
	}

	return stringSlice
}

// checkApplicationLimits checks if user can submit more applications
func (s *applicationService) checkApplicationLimits(ctx context.Context, userID uint) error {
	// Get user's applications count for today
	// TODO: Implement daily application limit checking
	return nil
}

// createResumeSnapshot creates a JSON snapshot of user's resume
func (s *applicationService) createResumeSnapshot(resume *model.Resume) (datatypes.JSON, error) {
	snapshot := map[string]interface{}{
		"name":                resume.Name,
		"age":                 resume.Age,
		"gender":              resume.Gender,
		"phone":               resume.Phone,
		"email":               resume.Email,
		"avatar_url":          resume.AvatarURL,
		"work_experience":     resume.WorkExperience,
		"education_history":   resume.EducationHistory,
		"skills":              resume.Skills,
		"job_intentions":      resume.JobIntentions,
		"expected_salary_min": resume.ExpectedSalaryMin,
		"expected_salary_max": resume.ExpectedSalaryMax,
		"preferred_locations": resume.PreferredLocations,
		"work_status":         resume.WorkStatus,
		"availability_date":   resume.AvailabilityDate,
		"self_introduction":   resume.SelfIntroduction,
		"snapshot_created_at": time.Now(),
	}

	snapshotJSON, err := json.Marshal(snapshot)
	if err != nil {
		return nil, fmt.Errorf("marshal resume snapshot failed: %w", err)
	}

	return datatypes.JSON(snapshotJSON), nil
}

// sendApplicationNotification sends notification to recruiter about new application
func (s *applicationService) sendApplicationNotification(ctx context.Context, application *model.JobApplication, job *model.Job) error {
	notification := &model.JobNotification{
		UserID:    job.UserID,
		Title:     "新的职位申请",
		Content:   fmt.Sprintf("您发布的职位「%s」收到新的申请", job.Title),
		Type:      constants.NotificationTypeApplication,
		RelatedID: &application.ID,
		IsRead:    false,
	}

	return s.applicationRepo.CreateNotification(ctx, notification)
}

// sendStatusUpdateNotification sends notification to applicant about status update
func (s *applicationService) sendStatusUpdateNotification(ctx context.Context, application *model.JobApplication, status, note string) error {
	statusLabel := constants.GetApplicationStatusLabel(status)
	content := fmt.Sprintf("您申请的职位「%s」状态已更新为：%s", application.Job.Title, statusLabel)
	if note != "" {
		content += fmt.Sprintf("\n备注：%s", note)
	}

	notification := &model.JobNotification{
		UserID:    application.UserID,
		Title:     "申请状态更新",
		Content:   content,
		Type:      constants.NotificationTypeApplication,
		RelatedID: &application.ID,
		IsRead:    false,
	}

	return s.applicationRepo.CreateNotification(ctx, notification)
}

// sendInterviewNotification sends interview notification to applicant
func (s *applicationService) sendInterviewNotification(ctx context.Context, application *model.JobApplication, interviewTime time.Time, address string) error {
	content := fmt.Sprintf("您申请的职位「%s」面试安排如下：\n时间：%s\n地点：%s",
		application.Job.Title,
		interviewTime.Format("2006-01-02 15:04"),
		address)

	notification := &model.JobNotification{
		UserID:    application.UserID,
		Title:     "面试通知",
		Content:   content,
		Type:      constants.NotificationTypeInterview,
		RelatedID: &application.ID,
		IsRead:    false,
	}

	return s.applicationRepo.CreateNotification(ctx, notification)
}

// convertApplicationToResponse converts application model to response format
func (s *applicationService) convertApplicationToResponse(application *model.JobApplication) *types.ApplicationResponse {
	return &types.ApplicationResponse{
		ID:               application.ID,
		JobID:            application.JobID,
		UserID:           application.UserID,
		Status:           application.Status,
		StatusLabel:      "", // Label should be computed on frontend
		StatusColor:      constants.GetApplicationStatusColor(application.Status),
		ResumeSnapshot:   application.ResumeSnapshot,
		RecruiterNote:    application.RecruiterNote,
		InterviewTime:    application.InterviewTime,
		InterviewAddress: application.InterviewAddress,
		CreatedAt:        application.CreatedAt,
		UpdatedAt:        application.UpdatedAt,
		Job:              s.convertJobToResponse(application.Job),
		User:             s.convertUserToInfo(application.User),
	}
}

// convertFavoriteToResponse converts favorite model to response format
func (s *applicationService) convertFavoriteToResponse(favorite *model.JobFavorite) *types.FavoriteResponse {
	return &types.FavoriteResponse{
		ID:        favorite.ID,
		UserID:    favorite.UserID,
		JobID:     favorite.JobID,
		CreatedAt: favorite.CreatedAt,
		Job:       s.convertJobToResponse(favorite.Job),
	}
}

// convertNotificationToResponse converts notification model to response format
func (s *applicationService) convertNotificationToResponse(notification *model.JobNotification) *types.NotificationResponse {
	return &types.NotificationResponse{
		ID:        notification.ID,
		UserID:    notification.UserID,
		Title:     notification.Title,
		Content:   notification.Content,
		Type:      notification.Type,
		TypeLabel: "", // TODO: Implement notification type label mapping
		RelatedID: notification.RelatedID,
		IsRead:    notification.IsRead,
		CreatedAt: notification.CreatedAt,
	}
}

// convertJobToResponse converts job model to response (simplified)
func (s *applicationService) convertJobToResponse(job *model.Job) *types.JobResponse {
	if job == nil {
		return nil
	}

	return &types.JobResponse{
		ID:                 job.ID,
		EnterpriseID:       job.EnterpriseID,
		UserID:             job.UserID,
		Title:              job.Title,
		Description:        job.Description,
		Status:             job.Status,
		StatusLabel:        "", // Label should be computed on frontend
		StatusColor:        "", // Color should be determined on frontend
		SalaryMin:          job.SalaryMin,
		SalaryMax:          job.SalaryMax,
		SalaryText:         fmt.Sprintf("%d-%d元/月", job.SalaryMin, job.SalaryMax),
		ExperienceReq:      job.ExperienceReq,
		ExperienceLabel:    "", // Label should be computed on frontend
		EducationReq:       job.EducationReq,
		EducationLabel:     "", // Label should be computed on frontend
		WorkType:           job.WorkType,
		WorkTypeLabel:      "", // Label should be computed on frontend
		WorkLocation:       job.WorkLocation,
		Latitude:           job.Latitude,
		Longitude:          job.Longitude,
		RemoteWorkSupport:  job.RemoteWorkSupport,
		Benefits:           convertJSONToStringSlice(job.Benefits),
		JobHighlights:      convertJSONToStringSlice(job.JobHighlights),
		Requirements:       convertJSONToStringSlice(job.Requirements),
		ContactMethod:      job.ContactMethod,
		ContactMethodLabel: "", // Label should be computed on frontend
		IsUrgent:           job.IsUrgent,
		UrgentExpiresAt:    job.UrgentExpiresAt,
		LastRefreshedAt:    job.LastRefreshedAt,
		TodayRefreshCount:  job.TodayRefreshCount,
		ViewCount:          job.ViewCount,
		ApplicationCount:   job.ApplicationCount,
		CreatedAt:          job.CreatedAt,
		UpdatedAt:          job.UpdatedAt,
	}
}

// convertUserToInfo converts user model to info (simplified)
func (s *applicationService) convertUserToInfo(user *model.User) *types.UserInfo {
	if user == nil {
		return nil
	}

	return &types.UserInfo{
		ID:       user.ID,
		Nickname: user.Nickname,
		Avatar:   user.Avatar,
		Phone:    user.Phone,
	}
}

// Placeholder implementations for remaining interface methods

func (s *applicationService) CancelApplication(ctx context.Context, userID uint, applicationID uint) error {
	// TODO: Implement application cancellation
	return errors.New("功能暂未实现")
}

func (s *applicationService) GetEnterpriseApplications(ctx context.Context, userID uint, req *types.ApplicationQueryRequest) (*types.ApplicationListResponse, error) {
	// Get user's enterprise
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	// Set default values
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	applications, total, err := s.applicationRepo.GetEnterpriseApplications(ctx, enterprise.ID, req)
	if err != nil {
		return nil, fmt.Errorf("获取企业申请列表失败: %w", err)
	}

	// Convert to response format
	applicationResponses := make([]types.ApplicationResponse, 0, len(applications))
	for _, application := range applications {
		response := s.convertApplicationToResponse(application)
		applicationResponses = append(applicationResponses, *response)
	}

	return &types.ApplicationListResponse{
		PaginationResp: types.NewPaginationResp(applicationResponses, total, req.Page, req.PageSize),
	}, nil
}

func (s *applicationService) GetApplicationTrends(ctx context.Context, userID uint, days int) ([]*model.ApplicationTrend, error) {
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	return s.applicationRepo.GetApplicationTrends(ctx, enterprise.ID, days)
}

func (s *applicationService) BatchUpdateApplicationStatus(ctx context.Context, userID uint, req *types.BulkActionRequest) error {
	// TODO: Implement batch update with permission checks
	return errors.New("功能暂未实现")
}

func (s *applicationService) BatchRejectApplications(ctx context.Context, userID uint, jobID uint, reason string) error {
	// TODO: Implement batch rejection
	return errors.New("功能暂未实现")
}

func (s *applicationService) MarkAllNotificationsAsRead(ctx context.Context, userID uint) error {
	// TODO: Implement mark all as read
	return errors.New("功能暂未实现")
}

func (s *applicationService) GetMyViewHistory(ctx context.Context, userID uint, page, pageSize int) ([]*types.ViewHistoryResponse, int64, error) {
	// TODO: Implement view history retrieval
	return []*types.ViewHistoryResponse{}, 0, errors.New("功能暂未实现")
}

func (s *applicationService) ClearViewHistory(ctx context.Context, userID uint) error {
	// TODO: Implement view history clearing
	return errors.New("功能暂未实现")
}

func (s *applicationService) GetMyReports(ctx context.Context, userID uint, page, pageSize int) ([]*model.JobReport, int64, error) {
	// TODO: Implement user reports retrieval
	return []*model.JobReport{}, 0, errors.New("功能暂未实现")
}

func (s *applicationService) GetReportsByStatus(ctx context.Context, status string, page, pageSize int) ([]*model.JobReport, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	return s.applicationRepo.GetReportsByStatus(ctx, status, page, pageSize)
}

func (s *applicationService) HandleReport(ctx context.Context, reportID uint, status, response string) error {
	return s.applicationRepo.UpdateReportStatus(ctx, reportID, status, response)
}

func (s *applicationService) SendMessageToRecruiter(ctx context.Context, userID uint, jobID uint, message string) error {
	// TODO: Implement messaging system
	return errors.New("功能暂未实现")
}

func (s *applicationService) SendBulkNotification(ctx context.Context, userIDs []uint, title, content, notificationType string) error {
	// TODO: Implement bulk notification sending
	return errors.New("功能暂未实现")
}
