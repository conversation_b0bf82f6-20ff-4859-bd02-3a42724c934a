package config

import (
	"log"
	"os"
	"time"

	"github.com/spf13/viper"
)

type Config struct {
	Server       ServerConfig       `mapstructure:"server"`
	Database     DatabaseConfig     `mapstructure:"database"`
	Redis        RedisConfig        `mapstructure:"redis"`
	JWT          JWTConfig          `mapstructure:"jwt"`
	Storage      StorageConfig      `mapstructure:"storage"`
	SMS          SMSConfig          `mapstructure:"sms"`
	Wechat       WechatConfig       `mapstructure:"wechat"`
	Logger       LoggerConfig       `mapstructure:"logger"`
	Centrifugo   CentrifugoConfig   `mapstructure:"centrifugo"`
	CORS         CORSConfig         `mapstructure:"cors"`
	RateLimit    RateLimitConfig    `mapstructure:"rate_limit"`
	Security     SecurityConfig     `mapstructure:"security"`
	FileUpload   FileUploadConfig   `mapstructure:"file_upload"`
	Cache        CacheConfig        `mapstructure:"cache"`
	Notification NotificationConfig `mapstructure:"notification"`
	Monitoring   MonitoringConfig   `mapstructure:"monitoring"`
	Swagger      SwaggerConfig      `mapstructure:"swagger"`
	DevLogin     DevLoginConfig     `mapstructure:"dev_login"`
}

type ServerConfig struct {
	Port         string        `mapstructure:"port"`
	Mode         string        `mapstructure:"mode"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

type DatabaseConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	User         string `mapstructure:"user"`
	Password     string `mapstructure:"password"`
	DBName       string `mapstructure:"dbname"`
	SSLMode      string `mapstructure:"sslmode"`
	MaxIdleConns int    `mapstructure:"max_idle_conns"`
	MaxOpenConns int    `mapstructure:"max_open_conns"`
}

type RedisConfig struct {
	Addr     string `mapstructure:"addr"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	PoolSize int    `mapstructure:"pool_size"`
}

type JWTConfig struct {
	Secret string        `mapstructure:"secret"`
	TTL    time.Duration `mapstructure:"ttl"`
}

type StorageConfig struct {
	AccessKey    string `mapstructure:"access_key"`
	SecretKey    string `mapstructure:"secret_key"`
	Bucket       string `mapstructure:"bucket"`
	Domain       string `mapstructure:"domain"`
	TokenExpires int64  `mapstructure:"token_expires"`
}

type SMSConfig struct {
	Aliyun AliyunSMSConfig `mapstructure:"aliyun"`
}

type AliyunSMSConfig struct {
	AccessKey    string `mapstructure:"access_key"`
	SecretKey    string `mapstructure:"secret_key"`
	SignName     string `mapstructure:"sign_name"`
	TemplateCode string `mapstructure:"template_code"`
}

type WechatConfig struct {
	AppID     string `mapstructure:"app_id"`
	AppSecret string `mapstructure:"app_secret"`
	MchID     string `mapstructure:"mch_id"`
	APIKey    string `mapstructure:"api_key"`
}

type AlipayConfig struct {
	AppID      string `mapstructure:"app_id"`
	PrivateKey string `mapstructure:"private_key"`
	PublicKey  string `mapstructure:"public_key"`
}

type LoggerConfig struct {
	Level            string         `mapstructure:"level"`
	Format           string         `mapstructure:"format"`
	OutputPaths      []string       `mapstructure:"output_paths"`
	ErrorOutputPaths []string       `mapstructure:"error_output_paths"`
	Rotation         RotationConfig `mapstructure:"rotation"`
}

type RotationConfig struct {
	MaxSize    int  `mapstructure:"max_size"`
	MaxAge     int  `mapstructure:"max_age"`
	MaxBackups int  `mapstructure:"max_backups"`
	Compress   bool `mapstructure:"compress"`
}

type CentrifugoConfig struct {
	URL        string `mapstructure:"url"`
	APIKey     string `mapstructure:"api_key"`
	HMACSecret string `mapstructure:"hmac_secret"`
	GRPCPort   int    `mapstructure:"grpc_port"`
}

type CORSConfig struct {
	Enabled          bool     `mapstructure:"enabled"`
	AllowedOrigins   []string `mapstructure:"allowed_origins"`
	AllowedMethods   []string `mapstructure:"allowed_methods"`
	AllowedHeaders   []string `mapstructure:"allowed_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
}

type RateLimitConfig struct {
	Enabled           bool `mapstructure:"enabled"`
	RequestsPerSecond int  `mapstructure:"requests_per_second"`
	Burst             int  `mapstructure:"burst"`
}

type SecurityConfig struct {
	BcryptCost             int           `mapstructure:"bcrypt_cost"`
	MaxLoginAttempts       int           `mapstructure:"max_login_attempts"`
	LockoutDuration        time.Duration `mapstructure:"lockout_duration"`
	PasswordMinLength      int           `mapstructure:"password_min_length"`
	PasswordRequireSpecial bool          `mapstructure:"password_require_special"`
	PasswordRequireNumber  bool          `mapstructure:"password_require_number"`
	PasswordRequireUpper   bool          `mapstructure:"password_require_uppercase"`
	PasswordRequireLower   bool          `mapstructure:"password_require_lowercase"`
}

type FileUploadConfig struct {
	MaxFileSize  int64    `mapstructure:"max_file_size"`
	AllowedTypes []string `mapstructure:"allowed_types"`
	UploadPath   string   `mapstructure:"upload_path"`
}

type CacheConfig struct {
	Enabled bool          `mapstructure:"enabled"`
	TTL     time.Duration `mapstructure:"ttl"`
	Prefix  string        `mapstructure:"prefix"`
}

type NotificationConfig struct {
	Enabled     bool        `mapstructure:"enabled"`
	PushService string      `mapstructure:"push_service"`
	JPush       JPushConfig `mapstructure:"jpush"`
}

type JPushConfig struct {
	AppKey         string `mapstructure:"app_key"`
	MasterSecret   string `mapstructure:"master_secret"`
	ApnsProduction bool   `mapstructure:"apns_production"`
}

type MonitoringConfig struct {
	Enabled     bool              `mapstructure:"enabled"`
	Prometheus  PrometheusConfig  `mapstructure:"prometheus"`
	HealthCheck HealthCheckConfig `mapstructure:"health_check"`
}

type PrometheusConfig struct {
	Port string `mapstructure:"port"`
	Path string `mapstructure:"path"`
}

type HealthCheckConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Path    string `mapstructure:"path"`
}

type SwaggerConfig struct {
	Enabled     bool   `mapstructure:"enabled"`
	Title       string `mapstructure:"title"`
	Description string `mapstructure:"description"`
	Version     string `mapstructure:"version"`
	Host        string `mapstructure:"host"`
	BasePath    string `mapstructure:"base_path"`
}

type DevLoginConfig struct {
	Enabled      bool     `mapstructure:"enabled"`
	AllowedUsers []string `mapstructure:"allowed_users"`
}

var Conf *Config

func Load() *Config {
	env := os.Getenv("GO_ENV")
	if env == "" {
		env = "dev"
	}

	configName := "config"
	if env == "dev" {
		configName = "config.dev"
	} else if env == "prod" {
		configName = "config.prod"
	}

	viper.SetConfigName(configName)
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("./")

	// 自动绑定环境变量
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("Error reading config file: %v", err)
	}

	if err := viper.Unmarshal(&Conf); err != nil {
		log.Fatalf("Error unmarshaling config: %v", err)
	}
	return Conf
}
