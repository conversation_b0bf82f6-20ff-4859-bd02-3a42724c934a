{"permissions": {"allow": ["Bash(rm:*)", "Bash(go mod:*)", "Bash(go build:*)", "Bash(go generate:*)", "<PERSON><PERSON>(make:*)", "Bash(go install:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(cd:*)", "Bash(git commit:*)", "Bash(go fmt:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(psql:*)", "Bash(npm run type-check:*)"], "deny": []}}