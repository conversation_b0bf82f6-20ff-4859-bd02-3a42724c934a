<template>
  <view class="container">
    <uni-nav-bar title="我的发布" :border="false"
    statusBar
    fixed
    left-icon="back"
    @clickLeft="goBack"
     background-color="transparent"
    ></uni-nav-bar>
    <!-- 顶部统计卡片 -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-card">
          <view class="stat-value">{{ stats.completed }}</view>
          <view class="stat-label">已完成</view>
          <view class="stat-icon i-carbon-checkmark-filled"></view>
        </view>
        <view class="stat-card">
          <view class="stat-value">{{ stats.inProgress }}</view>
          <view class="stat-label">进行中</view>
          <view class="stat-icon i-carbon-in-progress"></view>
        </view>
        <view class="stat-card">
          <view class="stat-value">{{ stats.recruiting }}</view>
          <view class="stat-label">招募中</view>
          <view class="stat-icon i-carbon-user-multiple"></view>
        </view>
      </view>
    </view>

    <!-- 筛选标签栏 -->
    <view class="filter-section">
      <tui-tab
        :tabs="tabs"
        :current="activeTabIndex"
        item-width="25%"
        @change="handleTabChange"
        :scroll="true"
        :color="'var(--text-secondary)'"
        :selectedColor="'var(--primary)'"
        :backgroundColor="'transparent'"
        :selectedBackgroundColor="'var(--bg-primary-light)'"
        :borderRadius="16"
      />
    </view>

    <!-- 零工列表 -->
    <view class="content-section">
      <!-- 空状态 -->
      <view v-if="filteredGigs.length === 0" class="empty-state">
        <view class="empty-illustration">
          <text class="empty-icon i-carbon-document-blank"></text>
        </view>
        <view class="empty-content">
          <text class="empty-title">{{ getEmptyStateText() }}</text>
          <text class="empty-desc">{{ getEmptyStateDesc() }}</text>
        </view>
        <view class="empty-actions">
          <button class="publish-btn" @tap="goToPublish">
            <text class="publish-icon i-carbon-add"></text>
            <text class="publish-text">发布新工作</text>
          </button>
        </view>
      </view>

      <!-- 零工卡片列表 -->
      <view v-else class="gig-list">
        <view
          v-for="gig in filteredGigs"
          :key="gig.id"
          class="gig-card"
          @tap="goToDetail(gig.id)"
        >
          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="title-section">
              <text class="gig-title">{{ gig.title }}</text>
              <view class="gig-meta">
                <text class="publish-time">{{
                  formatRelativeTime(gig.created_at)
                }}</text>
              </view>
            </view>
            <view
              class="status-badge"
              :class="getGigStatusDetails(gig.status).class"
            >
              <text class="status-text">{{
                getGigStatusDetails(gig.status).text
              }}</text>
            </view>
          </view>

          <!-- 关键信息 -->
          <view class="key-info">
            <view class="info-item">
              <text class="info-icon i-carbon-money"></text>
              <text class="info-text salary-text">{{
                formatSalary(gig.salary, gig.salary_unit)
              }}</text>
            </view>
            <view class="info-item">
              <text class="info-icon i-carbon-time"></text>
              <text class="info-text">{{
                formatWorkTimeRange(gig.start_time, gig.end_time, "simple")
              }}</text>
            </view>
            <view class="info-item">
              <text class="info-icon i-carbon-location"></text>
              <text class="info-text">{{
                gig.address_name || "地点待定"
              }}</text>
            </view>
            <view class="info-item">
              <text class="info-icon i-carbon-group-account"></text>
              <text class="info-text"
                >{{ gig.current_people_count }}/{{ gig.people_count }}人</text
              >
            </view>
          </view>

          <!-- 标签 -->
          <view v-if="gig.tags && gig.tags.length > 0" class="tags-section">
            <view v-for="tag in gig.tags.slice(0, 3)" :key="tag" class="tag">
              {{ tag }}
            </view>
            <view v-if="gig.tags.length > 3" class="tag tag-more">
              +{{ gig.tags.length - 3 }}
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-section">
            <view
              v-for="button in getActionButtons(gig)"
              :key="button.text"
              class="action-btn"
              :class="button.type"
              @tap.stop="handleAction(button.action, gig)"
            >
              <text class="btn-text">{{ button.text }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { listMyGigs, deleteGig as deleteGigAPI } from "@/api/gig";
import { GigStatus, getGigStatusDetails } from "@/constants/gig";
import { formatWorkTimeRange } from "@/utils/format";
import { formatSalary } from "@/utils/gig";
import { formatRelativeTime } from "@/utils/date";
import { showDeleteConfirm, showSuccessToast, withLoading } from "@/utils/ui";
import { navigateTo, buildUrl } from "@/utils/navigation";
import { deleteItem } from "@/utils/api-helper";
import type { Gig } from "@/types/gig";
import { getGigActions } from "@/services/gig";

// 定义状态和数据
const activeTab = ref<string>("all");
const allGigs = ref<Gig[]>([]);

// 筛选标签
const tabs = [
  { key: "all", name: "全部" },
  { key: GigStatus.Recruiting, name: "招募中" },
  { key: GigStatus.InProgress, name: "进行中" },
  { key: GigStatus.Completed, name: "已完成" },
  { key: GigStatus.Paused, name: "暂停中" },
  { key: GigStatus.Closed, name: "已关闭" },
];

onLoad(() => {
  fetchGigs();
});

const activeTabIndex = computed(() =>
  tabs.findIndex((t) => t.key === activeTab.value)
);

const handleTabChange = (e: { index: number }) => {
  activeTab.value = tabs[e.index].key;
};



// 空状态文本
const getEmptyStateText = () => {
  switch (activeTab.value) {
    case "recruiting":
      return "暂无招募中的工作";
    case "in_progress":
      return "暂无进行中的工作";
    case "completed":
      return "暂无已完成的工作";
    case "closed":
      return "暂无已关闭的工作";
    default:
      return "暂无工作记录";
  }
};

const getEmptyStateDesc = () => {
  switch (activeTab.value) {
    case "recruiting":
      return "发布新工作开始招募合适的人才";
    case "in_progress":
      return "当前没有正在进行的工作";
    case "completed":
      return "还没有完成的工作记录";
    case "closed":
      return "没有已关闭的工作";
    default:
      return "发布您的第一个工作吧";
  }
};

// 跳转到发布页面
const goToPublish = () => {
  navigateTo("/pages/gig/publish");
};

// 跳转到详情页面
const goToDetail = (gigId: number) => {
  navigateTo(buildUrl("/pages/gig/detail", { id: gigId }));
};

// 根据当前标签过滤零工
const filteredGigs = computed(() => {
  if (activeTab.value === "all") {
    return allGigs.value;
  }
  return allGigs.value.filter((gig) => gig.status === activeTab.value);
});

// 统计数据
const stats = computed(() => {
  return {
    completed: allGigs.value.filter((g) => g.status === GigStatus.Completed)
      .length,
    inProgress: allGigs.value.filter((g) => g.status === GigStatus.InProgress)
      .length,
    recruiting: allGigs.value.filter((g) => g.status === GigStatus.Recruiting)
      .length,
  };
});

// 根据状态获取操作按钮
const getActionButtons = (gig: Gig) => {
  return getGigActions(gig, true); // true表示是发布者
};

const fetchGigs = async () => {
  const { data } = await withLoading(
    () => listMyGigs({ page: 1, page_size: 100 }),
    "暂无零工数据"
  );
  allGigs.value = data?.list || [];
};

const handleAction = (action: string, gig: Gig) => {
  console.log(`Action: ${action}, Gig ID: ${gig.id}`);
  switch (action) {
    case "viewApplicants":
      navigateTo(buildUrl("/pages/gig/applicants", { gigId: gig.id }));
      break;
    case "edit":
      navigateTo(buildUrl("/pages/gig/publish", { id: gig.id }));
      break;
    case "delete":
      showDeleteConfirm("该零工", async () => {
        await deleteItem(
          () => deleteGigAPI(gig.id),
          "零工",
          () => fetchGigs()
        );
      });
      break;
    case "republish":
      navigateTo(
        buildUrl("/pages/gig/publish", { republish: true, id: gig.id })
      );
      break;
    // TODO: 实现其他操作的逻辑
    case "pause":
    case "resume":
    case "close":
    case "contact":
    case "publish":
    case "viewDetail":
      showSuccessToast(`操作"${action}"待实现`);
      break;
  }
};



// 回退函数
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-50), var(--bg-page));
}

// 统计卡片区域
.stats-section {
  padding: 24rpx;
  // background: linear-gradient(135deg, var(--primary-50), var(--bg-page));

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;

    .stat-card {
      position: relative;
      background: var(--bg-card);
      border-radius: 20rpx;
      padding: 32rpx 24rpx;
      text-align: center;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      overflow: hidden;

      .stat-value {
        font-size: 48rpx;
        font-weight: 700;
        color: var(--text-base);
        margin-bottom: 8rpx;
        display: block;
      }

      .stat-label {
        font-size: 24rpx;
        color: var(--text-secondary);
        font-weight: 500;
      }

      .stat-icon {
        position: absolute;
        top: 16rpx;
        right: 16rpx;
        font-size: 32rpx;
        color: var(--primary);
        opacity: 0.3;
      }

      &:nth-child(1) .stat-icon {
        color: var(--text-green);
      }
      &:nth-child(2) .stat-icon {
        color: var(--text-blue);
      }
      &:nth-child(3) .stat-icon {
        color: var(--primary);
      }
    }
  }
}

// 筛选标签栏
.filter-section {
  padding: 0 24rpx 24rpx;
}

// 内容区域
.content-section {
  padding: 0 24rpx 24rpx;
}

// 空状态页面
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 80rpx 40rpx;
  text-align: center;

  .empty-illustration {
    margin-bottom: 40rpx;

    .empty-icon {
      font-size: 160rpx;
      color: var(--text-info);
      opacity: 0.6;
    }
  }

  .empty-content {
    margin-bottom: 48rpx;

    .empty-title {
      font-size: 36rpx;
      font-weight: 600;
      color: var(--text-base);
      margin-bottom: 16rpx;
      display: block;
    }

    .empty-desc {
      font-size: 28rpx;
      color: var(--text-secondary);
      line-height: 1.5;
      max-width: 500rpx;
    }
  }

  .empty-actions {
    .publish-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      background: linear-gradient(135deg, var(--primary), #ff8533);
      color: white;
      border: none;
      border-radius: 44rpx;
      padding: 20rpx 40rpx;
      font-size: 28rpx;
      font-weight: 600;
      box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.4);
      }

      .publish-icon {
        font-size: 32rpx;
        color: white;
      }

      .publish-text {
        color: white;
      }
    }
  }
}

// 零工列表
.gig-list {
  .gig-card {
    background: var(--bg-card);
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    }

    // 卡片头部
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24rpx;

      .title-section {
        flex: 1;
        margin-right: 16rpx;

        .gig-title {
          font-size: 36rpx;
          font-weight: 600;
          color: var(--text-base);
          line-height: 1.3;
          margin-bottom: 8rpx;
          display: block;
        }

        .gig-meta {
          .publish-time {
            font-size: 24rpx;
            color: var(--text-info);
          }
        }
      }

      .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 600;
        flex-shrink: 0;

        .status-text {
          color: inherit;
        }

        &.status-recruiting {
          background: var(--bg-success-light);
          color: var(--text-green);
        }

        &.status-paused {
          background: var(--bg-warning-light);
          color: var(--text-yellow);
        }

        &.status-locked {
          background: var(--bg-info-light);
          color: var(--text-blue);
        }

        &.status-ongoing {
          background: var(--bg-info-light);
          color: var(--text-blue);
        }

        &.status-completed {
          background: var(--bg-primary-light);
          color: var(--primary);
        }

        &.status-closed {
          background: var(--bg-tag);
          color: var(--text-info);
        }
      }
    }

    // 关键信息
    .key-info {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20rpx 16rpx;
      margin-bottom: 24rpx;

      .info-item {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .info-icon {
          font-size: 28rpx;
          color: var(--primary);
          flex-shrink: 0;
        }

        .info-text {
          font-size: 26rpx;
          color: var(--text-base);
          font-weight: 500;
          flex: 1;
          min-width: 0;
          word-break: break-all;

          &.salary-text {
            color: var(--primary);
            font-weight: 600;
          }
        }
      }
    }

    // 标签区域
    .tags-section {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;
      margin-bottom: 24rpx;

      .tag {
        background: var(--bg-tag);
        color: var(--text-secondary);
        padding: 6rpx 12rpx;
        border-radius: 16rpx;
        font-size: 22rpx;
        font-weight: 500;

        &.tag-more {
          background: var(--bg-primary-light);
          color: var(--primary);
        }
      }
    }

    // 操作按钮
    .action-section {
      display: flex;
      justify-content: flex-end;
      gap: 16rpx;

      .action-btn {
        border: none;
        border-radius: 32rpx;
        padding: 16rpx 32rpx;
        font-size: 26rpx;
        font-weight: 600;
        transition: all 0.3s ease;

        .btn-text {
          color: inherit;
        }

        &.primary {
          background: linear-gradient(135deg, var(--primary), #ff8533);
          color: white;
          box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.3);

          &:active {
            transform: scale(0.98);
            box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.4);
          }
        }

        &.secondary {
          background: var(--bg-tag);
          color: var(--text-base);

          &:active {
            background: var(--text-info);

            .btn-text {
              color: white;
            }
          }
        }

        &.danger {
          background: var(--bg-danger-light);
          color: var(--text-red);

          &:active {
            background: var(--text-red);

            .btn-text {
              color: white;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .stats-section .stats-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }

  .gig-list .gig-card {
    .key-info {
      grid-template-columns: 1fr;
      gap: 20rpx;
    }

    .action-section {
      flex-direction: column;
      align-items: stretch;

      .action-btn {
        width: 100%;
        text-align: center;
      }
    }
  }
}
</style>
