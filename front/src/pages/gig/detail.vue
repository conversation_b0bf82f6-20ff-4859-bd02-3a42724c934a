<template>
  <view class="container">

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="hasError" class="error-container">
      <text class="error-text">{{ errorMessage }}</text>
      <button class="retry-button" @click="fetchGigDetail">重试</button>
    </view>

    <!-- 正常内容 -->
    <scroll-view v-else-if="gig" scroll-y enable-flex class="detail-scroll-view">
      <!-- 主要信息卡片 -->
      <view class="main-info-card">
        <!-- 工作类型和状态 -->
        <view class="header-section">
          <text class="job-type">{{ gig.title || "家政保洁" }}</text>
          <view v-if="canApply" class="status-badge available">
            <text class="status-text">可报名</text>
          </view>
        </view>

        <!-- 经验要求 -->
        <text class="experience-text">{{
          gig.description || "有家政经验，做事认真细致"
        }}</text>

        <!-- 薪资信息 -->
        <view class="salary-section">
          <text class="salary-amount">{{
            formatSalary(gig.salary || 160, gig.salary_unit || 1)
          }}</text>
          <text class="salary-duration">{{ formatWorkDuration() }}</text>
        </view>

        <!-- 时间信息 -->
        <view class="time-section">
          <text class="time-icon i-carbon-time"></text>
          <text class="time-text">{{
            formatWorkTimeRange(gig.start_time, gig.end_time, "detailed") ||
            "2023年6月17日 13:00-16:00"
          }}</text>
        </view>

        <!-- 地点信息 -->
        <view class="location-section">
          <text class="location-icon i-carbon-location"></text>
          <text class="location-text">{{
            gig.address_name || "海淀区中关村创业大厦"
          }}</text>
          <text class="distance-text">·{{ gig.distance || "6.3" }}km</text>
        </view>
      </view>

      <!-- 任务说明 -->
      <view class="task-description-card">
        <text class="card-title">任务说明</text>
        <view class="task-list">
          <view class="task-item">
            <text class="task-number">1.</text>
            <text class="task-content"
              >主要负责办公室日常保洁工作，包括地面清洁、办公桌桌面擦拭等。</text
            >
          </view>
          <view class="task-item">
            <text class="task-number">2.</text>
            <text class="task-content"
              >保持办公区域干净整洁，定期清洁卫生间。</text
            >
          </view>
          <view class="task-item">
            <text class="task-number">3.</text>
            <text class="task-content"
              >整理会议室，会议后及时清理并恢复环境。</text
            >
          </view>
          <view class="task-item">
            <text class="task-number">4.</text>
            <text class="task-content"
              >需要自备基本清洁工具，清洁剂由用人方提供。</text
            >
          </view>
          <view class="task-item">
            <text class="task-number">5.</text>
            <text class="task-content"
              >工作具体负责，细心耐心，有良好的服务意识。</text
            >
          </view>
        </view>
      </view>

      <!-- 打卡方式 -->
      <view class="checkin-card">
        <text class="card-title">打卡方式</text>
        <view class="checkin-method">
          <text class="checkin-icon i-carbon-location-filled"></text>
          <text class="checkin-text">GPS定位打卡</text>
        </view>
      </view>

      <!-- 发布者信息 -->
      <view class="publisher-card">
        <view class="publisher-info" @tap="goToPublisherProfile">
          <image
            :src="gig.publisher?.avatar || '/static/images/default-avatar.png'"
            class="publisher-avatar"
            @error="handleAvatarError"
          />
          <view class="publisher-details">
            <view class="publisher-name-section">
              <text class="publisher-name">{{
                gig.publisher?.nickname || "张先生"
              }}</text>
              <view class="verified-badge">
                <text class="verified-icon i-carbon-checkmark-filled"></text>
                <text class="verified-text">已认证商家</text>
              </view>
            </view>
            <text class="publisher-stats"
              >发布零工46次</text
            >
          </view>
        </view>

        <!-- 联系电话 -->
        <view class="contact-section" @tap="handleCallPhone">
          <text class="contact-icon i-carbon-phone"></text>
          <text class="contact-text">联系电话</text>
          <text class="phone-number">{{
            formatPhoneNumber(gig.contact_phone || "13812345678")
          }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar safe-area-inset-bottom">
      <!-- 发布者视角的操作按钮 -->
      <template v-if="isPublisher">
        <button
          class="secondary-action-button"
          @tap="handleEditGig"
          :disabled="!canEdit"
        >
          <text class="button-icon i-carbon-edit"></text>
          <text class="button-text">编辑</text>
        </button>

        <button
          class="primary-action-button"
          @tap="handleManageApplications"
          :class="publisherActionButton.type"
        >
          <text class="button-text">{{ publisherActionButton.text }}</text>
        </button>
      </template>

      <!-- 求职者视角的操作按钮 -->
      <template v-else>
        <!-- 主要操作按钮 -->
        <button
          class="apply-button"
          @click="handleApply"
          :disabled="applyButton.disabled"
          :class="{ 'disabled-button': applyButton.disabled }"
        >
          <text class="button-text">{{ applyButton.text || "立即报名" }}</text>
        </button>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getGig, applyForGig, checkApplicationStatus } from "@/api/gig";
import { type Gig, type CheckApplicationStatusResponse } from "@/types/gig";
import {
  GigStatus,
  GigApplicationStatus,
} from "@/constants/gig";

import { formatSalary } from "@/utils/gig";
import { formatWorkTimeRange } from "@/utils/format";
import { fetchSingle, submitForm } from "@/utils/api-helper";
import { showConfirm,formatPhoneNumber,navigateTo } from "@/utils";
import { useUserStore } from "@/stores";


// 格式化工作时长（根据设计图显示）
function formatWorkDuration(): string {
  if (!gig.value) return "3小时·共¥480";

  const duration = gig.value.work_duration || 180; // 默认3小时
  const salary = gig.value.salary || 160;
  const hours = Math.round(duration / 60);
  const total = salary * hours;

  return `${hours}小时·共¥${total}`;
}


// 分享功能
const handleShare = () => {
  uni.showActionSheet({
    itemList: ["分享给朋友", "分享到朋友圈"],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 分享给朋友
        uni.showToast({
          title: "分享功能开发中",
          icon: "none",
        });
      } else if (res.tapIndex === 1) {
        // 分享到朋友圈
        uni.showToast({
          title: "分享功能开发中",
          icon: "none",
        });
      }
    },
  });
};

// 数据状态
const gig = ref<Gig | null>(null);
const gigId = ref<number>(0);
const isLoading = ref(true);
const hasError = ref(false);
const errorMessage = ref("加载失败");

// 申请状态
const applicationStatusInfo = ref<CheckApplicationStatusResponse | null>(null);

// 用户状态
const userStore = useUserStore();
const isPublisher = ref(false); // 是否为发布者

// 计算属性：申请状态
const hasApplied = computed(() => {
  return applicationStatusInfo.value?.has_applied || !!gig.value?.has_applied;
});

const applicationStatus = computed(() => {
  return applicationStatusInfo.value?.application_status || null;
});

// 是否可以申请
const canApply = computed(() => {
  if (!gig.value || isPublisher.value) return false;
  return gig.value.status === GigStatus.Recruiting && !hasApplied.value;
});

// 编辑工作
const handleEditGig = () => {
  if (!gig.value) return;

  navigateTo(`/pages/gig/publish?id=${gig.value.id}&mode=edit`);
};

// 管理申请
const handleManageApplications = () => {
  if (!gig.value) return;

  const status = gig.value.status;

  switch (status) {
    case GigStatus.Recruiting:
    case GigStatus.InProgress:
      navigateTo(`/pages/gig/applications?gigId=${gig.value.id}`);
      break;

    case GigStatus.Completed:
      navigateTo(`/pages/gig/records?gigId=${gig.value.id}`);
      break;

    case GigStatus.Paused:
      // 恢复招聘
      handleResumeRecruitment();
      break;

    case GigStatus.Closed:
      // 重新发布
      handleRepublish();
      break;

    default:
      navigateTo(`/pages/gig/applications?gigId=${gig.value.id}`);
  }
};

// 恢复招聘
const handleResumeRecruitment = async () => {
  if (!gig.value) return;

  try {
    // await showConfirm("确认恢复招聘？", "恢复后工作将重新开始招聘");

    uni.showToast({
      title: "已恢复招聘",
      icon: "success",
    });

    // 重新加载数据
    await fetchGigDetail();
  } catch (error) {
    console.error("恢复招聘失败:", error);
  }
};

// 重新发布
const handleRepublish = async () => {
  if (!gig.value) return;

  try {
    // await showConfirm("确认重新发布？", "将基于当前信息重新发布工作");

    // 跳转到发布页面，预填充当前信息
    navigateTo(`/pages/gig/publish?id=${gig.value.id}&mode=republish`);
  } catch (error) {
    console.error("重新发布失败:", error);
  }
};

onLoad(async (options) => {
  if (!options?.id) {
    uni.showToast({ title: "参数错误", icon: "none" });
    uni.navigateBack();
    return;
  }

  const id = parseInt(options.id, 10);
  if (isNaN(id) || id <= 0) {
    uni.showToast({ title: "无效的零工ID", icon: "none" });
    uni.navigateBack();
    return;
  }

  gigId.value = id;
  await fetchGigDetail();
});

async function fetchGigDetail() {
  if (!gigId.value) return;

  try {
    isLoading.value = true;
    hasError.value = false;

    // 并行获取零工详情和申请状态
    const [gigResult, statusResult] = await Promise.allSettled([
      fetchSingle(() => getGig(gigId.value), "获取零工详情失败"),
      fetchApplicationStatus(),
    ]);

    // 处理零工详情
    if (gigResult.status === "fulfilled" && gigResult.value?.data) {
      gig.value = gigResult.value.data;

      // 检查用户是否为发布者
      const { user } = userStore;
      isPublisher.value = !!(user?.id && gig.value.user_id === user.id);
    } else {
      throw new Error("无法获取零工详情");
    }

    // 处理申请状态（失败不影响主流程）
    if (statusResult.status === "fulfilled") {
      // 申请状态获取成功，已在 fetchApplicationStatus 中处理
    } else {
      console.warn("获取申请状态失败:", statusResult.reason);
    }
  } catch (error) {
    console.error("获取零工详情失败:", error);
    hasError.value = true;
    errorMessage.value =
      error instanceof Error ? error.message : "加载失败，请稍后再试";
  } finally {
    isLoading.value = false;
  }
}

// 获取申请状态
async function fetchApplicationStatus() {
  if (!gigId.value) return;

  const { user } = userStore;
  if (!user?.id) return; // 未登录用户不需要获取申请状态

  try {
    const result = await checkApplicationStatus(gigId.value);
    if (result.code === 0 && result.data) {
      applicationStatusInfo.value = result.data;
    }
  } catch (error) {
    console.warn("获取申请状态失败:", error);
    // 申请状态获取失败不影响主流程
  }
}

// 错误处理函数
const handleImageError = (e: any) => {
  console.warn("图片加载失败:", e);
  // 可以在这里设置默认图片
};

const handleAvatarError = (e: any) => {
  console.warn("头像加载失败:", e);
  // 可以设置默认头像
};

// 底部操作按钮逻辑
const applyButton = computed(() => {
  if (isLoading.value) {
    return { text: "加载中...", disabled: true, type: "loading" };
  }

  if (!gig.value) {
    return { text: "数据加载失败", disabled: true, type: "error" };
  }

  // 发布者看到的按钮
  if (isPublisher.value) {
    return { text: "管理申请", disabled: false, type: "publisher" };
  }

  // 普通用户已申请的情况
  if (hasApplied.value) {
    return { text: "已报名", disabled: true, type: "applied" };
  }

  // 普通用户未申请的情况
  switch (gig.value.status) {
    case GigStatus.Recruiting:
      return { text: "立即报名", disabled: false, type: "primary" };
    case GigStatus.Paused:
      return { text: "暂停招募", disabled: true, type: "paused" };
    case GigStatus.Locked:
      return { text: "招募截止", disabled: true, type: "locked" };
    case GigStatus.InProgress:
      return { text: "进行中", disabled: true, type: "progress" };
    case GigStatus.Completed:
      return { text: "已完成", disabled: true, type: "completed" };
    case GigStatus.Closed:
      return { text: "已关闭", disabled: true, type: "closed" };
    default:
      return { text: "无法报名", disabled: true, type: "disabled" };
  }
});

// 是否显示联系发布者按钮
const shouldShowContactButton = computed(() => {
  if (!gig.value || isPublisher.value) return false;

  // 只有已申请且审核通过的用户才能看到联系按钮
  return (
    hasApplied.value &&
    applicationStatus.value === GigApplicationStatus.Confirmed
  );
});

// 发布者操作按钮状态
const publisherActionButton = computed(() => {
  if (!gig.value) {
    return { text: "加载中...", type: "disabled" };
  }

  const status = gig.value.status;
  const currentCount = gig.value.current_people_count || 0;

  switch (status) {
    case GigStatus.Recruiting:
      if (currentCount > 0) {
        return { text: `管理申请 (${currentCount})`, type: "primary" };
      } else {
        return { text: "管理申请", type: "secondary" };
      }

    case GigStatus.InProgress:
      return { text: "管理工作", type: "primary" };

    case GigStatus.Completed:
      return { text: "查看记录", type: "secondary" };

    case GigStatus.Paused:
      return { text: "恢复招聘", type: "primary" };

    case GigStatus.Closed:
      return { text: "重新发布", type: "primary" };

    default:
      return { text: "管理", type: "secondary" };
  }
});

// 是否可以编辑
const canEdit = computed(() => {
  if (!gig.value || !isPublisher.value) return false;

  const status = gig.value.status;
  return status === GigStatus.Recruiting || status === GigStatus.Paused;
});

async function handleApply() {
  if (!gig.value || applyButton.value.disabled) return;

  const { user } = userStore;

  // 发布者点击管理申请
  if (isPublisher.value) {
    navigateTo(`/pages/gig/applicants?gigId=${gigId.value}`);
    return;
  }

  // 已申请用户的操作
  if (hasApplied.value) {
    // 目前无法判断具体申请状态，暂时显示提示
    uni.showToast({
      title: "您已申请过此工作，请耐心等待审核结果",
      icon: "none",
      duration: 2000,
    });
    return;
  }

  if (!user?.nickname || !user?.phone) {
    const confirmed = await showConfirm({
      title: "提示",
      content: "请先在'我的'页面完善您的姓名和联系方式再进行报名。",
      confirmText: "去完善",
      cancelText: "取消",
    });

    if (confirmed) {
      navigateTo("/pages/mine/profile");
    }
    return;
  }

  const confirmed = await showConfirm({
    title: "确认报名",
    content: `您确定要报名【${gig.value.title}】吗？`,
    confirmText: "确认报名",
  });

  if (confirmed) {
    const success = await submitForm(
      () =>
        applyForGig({
          gig_id: gig.value!.id,
          message: "我对此很感兴趣，希望获得这个机会！",
          applicant_name: user?.nickname,
          applicant_phone: user?.phone,
        }),
      "报名成功！",
      async () => {
        // 重新获取数据以更新状态
        await fetchGigDetail();
      }
    );
  }
}

// 拨打电话
async function handleCallPhone() {
  if (!gig.value?.contact_phone) {
    uni.showToast({ title: "暂无联系方式", icon: "none" });
    return;
  }

  uni.makePhoneCall({
    phoneNumber: gig.value.contact_phone,
    fail: (err) => {
      console.error("拨打电话失败:", err);
      uni.showToast({ title: "拨打电话失败", icon: "none" });
    },
  });
}

// 联系发布者
async function handleContactEmployer() {
  if (!gig.value?.contact_phone) {
    uni.showToast({ title: "暂无联系方式", icon: "none" });
    return;
  }

  const confirmed = await showConfirm({
    title: "联系发布者",
    content: `是否拨打 ${gig.value.contact_phone}？`,
    confirmText: "拨打电话",
    cancelText: "取消",
  });

  if (confirmed) {
    handleCallPhone();
  }
}

// 查看发布者资料
function goToPublisherProfile() {
  if (!gig.value?.publisher?.user_id) {
    uni.showToast({ title: "无法查看发布者信息", icon: "none" });
    return;
  }

  // TODO: 跳转到发布者详情页面
  uni.showToast({ title: "发布者详情页面开发中", icon: "none" });
}

// 取消申请
async function handleCancelApplication() {
  const confirmed = await showConfirm({
    title: "确认取消",
    content: "您确定要取消这个申请吗？取消后可能需要重新申请。",
    confirmText: "确认取消",
    cancelText: "继续保持",
  });

  if (confirmed) {
    // TODO: 调用取消申请的API
    // await cancelApplication(gigId.value);
    uni.showToast({ title: "取消成功", icon: "success" });
    // 重新获取数据以更新状态
    fetchGigDetail();
  }
}

</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: var(--bg-page);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;

  .detail-scroll-view {
    flex: 1;
    margin: 20rpx;
    margin-bottom: 140rpx; /* 为底部操作栏留出空间 */
    box-sizing: border-box;
    height: calc(100vh - 140rpx); /* 确保滚动区域高度正确 */
  }

  // 主要信息卡片
  .main-info-card {
    background: var(--bg-card);
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    width: 100%;
    box-sizing: border-box;

    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .job-type {
        font-size: 36rpx;
        font-weight: 700;
        color: var(--text-base);
      }

      .status-badge {
        &.available {
          background: var(--text-green);
          color: white;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          font-weight: 600;

          .status-text {
            color: white;
          }
        }
      }
    }

    .experience-text {
      font-size: 28rpx;
      color: var(--text-secondary);
      line-height: 1.5;
      margin-bottom: 24rpx;
      word-wrap: break-word;
      word-break: break-all;
    }

    .salary-section {
      display: flex;
      align-items: baseline;
      gap: 12rpx;
      margin-bottom: 24rpx;

      .salary-amount {
        font-size: 48rpx;
        font-weight: 700;
        color: var(--text-red);
      }

      .salary-duration {
        font-size: 28rpx;
        color: var(--text-secondary);
      }
    }

    .time-section,
    .location-section {
      display: flex;
      align-items: center;
      gap: 12rpx;
      margin-bottom: 16rpx;

      .time-icon,
      .location-icon {
        font-size: 32rpx;
        color: var(--text-info);
      }

      .time-text,
      .location-text {
        font-size: 28rpx;
        color: var(--text-base);
        word-wrap: break-word;
        flex: 1;
      }

      .distance-text {
        font-size: 28rpx;
        color: var(--text-info);
      }
    }

    .location-section {
      margin-bottom: 0;
    }
  }

  // 任务说明卡片
  .task-description-card {
    background: var(--bg-card);
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    width: 100%;
    box-sizing: border-box;

    .card-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-base);
      margin-bottom: 24rpx;
    }

    .task-list {
      .task-item {
        display: flex;
        align-items: flex-start;
        gap: 12rpx;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .task-number {
          font-size: 28rpx;
          color: var(--text-secondary);
          font-weight: 600;
          flex-shrink: 0;
          margin-top: 2rpx;
        }

        .task-content {
          font-size: 28rpx;
          color: var(--text-base);
          line-height: 1.5;
          flex: 1;
          word-wrap: break-word;
          word-break: break-all;
        }
      }
    }
  }

  // 打卡方式卡片
  .checkin-card {
    background: var(--bg-card);
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    width: 100%;
    box-sizing: border-box;

    .card-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-base);
      margin-bottom: 24rpx;
    }

    .checkin-method {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .checkin-icon {
        font-size: 32rpx;
        color: var(--text-blue);
      }

      .checkin-text {
        font-size: 28rpx;
        color: var(--text-base);
      }
    }
  }

  // 发布者信息卡片
  .publisher-card {
    background: var(--bg-card);
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    width: 100%;
    box-sizing: border-box;

    .publisher-info {
      display: flex;
      align-items: center;
      gap: 24rpx;
      margin-bottom: 24rpx;

      .publisher-avatar {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
        background: var(--bg-tag);
      }

      .publisher-details {
        flex: 1;

        .publisher-name-section {
          display: flex;
          align-items: center;
          gap: 12rpx;
          margin-bottom: 8rpx;

          .publisher-name {
            font-size: 32rpx;
            font-weight: 600;
            color: var(--text-base);
            word-wrap: break-word;
            flex: 1;
          }

          .verified-badge {
            display: flex;
            align-items: center;
            gap: 4rpx;
            background: var(--text-blue);
            color: white;
            padding: 4rpx 12rpx;
            border-radius: 16rpx;

            .verified-icon {
              font-size: 20rpx;
              color: white;
            }

            .verified-text {
              font-size: 20rpx;
              color: white;
            }
          }
        }

        .publisher-stats {
          font-size: 24rpx;
          color: var(--text-info);
          word-wrap: break-word;
        }
      }
    }

    .contact-section {
      display: flex;
      align-items: center;
      gap: 12rpx;
      padding: 24rpx;
      background: var(--bg-secondary);
      border-radius: 16rpx;

      .contact-icon {
        font-size: 32rpx;
        color: var(--text-blue);
      }

      .contact-text {
        font-size: 28rpx;
        color: var(--text-base);
        margin-right: auto;
        word-wrap: break-word;
      }

      .phone-number {
        font-size: 28rpx;
        color: var(--text-secondary);
        word-wrap: break-word;
      }
    }
  }

  // 底部操作栏
  .bottom-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-card);
    padding: 24rpx 32rpx;
    border-top: 1rpx solid var(--border-color);
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);
    display: flex;
    gap: 24rpx;
    z-index: 999;

    .apply-button {
      flex: 1;
      height: 88rpx;
      background: #4a90e2;
      color: white;
      border: none;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;

      &.disabled-button {
        background: var(--bg-disabled);
        color: var(--text-disabled);
      }

      .button-text {
        color: inherit;
      }
    }

    .secondary-action-button {
      flex: 1;
      height: 88rpx;
      background: var(--bg-secondary);
      color: var(--text-base);
      border: 1rpx solid var(--border-color);
      border-radius: 44rpx;
      font-size: 28rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;

      &:disabled {
        background: var(--bg-disabled);
        color: var(--text-disabled);
        border-color: var(--bg-disabled);
      }

      .button-icon {
        font-size: 28rpx;
        color: inherit;
      }

      .button-text {
        color: inherit;
      }
    }

    .primary-action-button {
      flex: 2;
      height: 88rpx;
      background: var(--primary);
      color: white;
      border: none;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;

      .button-text {
        color: white;
      }
    }
  }

  // 加载和错误状态
  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60vh;
    gap: 24rpx;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid var(--border-color);
      border-top: 4rpx solid var(--primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text,
    .error-text {
      font-size: 28rpx;
      color: var(--text-secondary);
    }

    .retry-button {
      background: var(--primary);
      color: white;
      border: none;
      padding: 16rpx 32rpx;
      border-radius: 24rpx;
      font-size: 28rpx;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
