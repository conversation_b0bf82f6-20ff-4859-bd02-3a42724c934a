<template>
  <view class="gig-card bg-card rounded-lg p-4 mb-4" @tap="handleCardClick">
    <!-- Card Header -->
    <view class="flex justify-between items-start mb-3">
      <view class="flex-1">
        <text class="text-lg font-bold text-base">{{ gig.title }}</text>
      </view>
      <view class="ml-4">
        <text class="text-lg font-bold text-red">{{ formattedSalary }}</text>
      </view>
    </view>

    <!-- Description -->
    <view class="mb-3">
      <text class="text-sm text-secondary">{{ truncatedDescription }}</text>
    </view>

    <!-- Info Section -->
    <view class="space-y-2 mb-4">
      <view class="flex items-center text-sm text-secondary">
        <text class="i-carbon-calendar w-5 h-5 mr-2"></text>
        <text>{{ formattedWorkTime }}</text>
      </view>
      <view class="flex items-center text-sm text-secondary">
        <text class="i-carbon-location w-5 h-5 mr-2"></text>
        <text>{{ gig.address_name }} · {{ gig.distance }}km</text>
      </view>
    </view>

    <!-- Footer -->
    <view class="flex justify-between items-center">
      <view>
        <text class="text-sm text-info">{{ formattedWorkDuration }}</text>
        <text class="text-red font-bold ml-2">共¥{{ gig.total_salary }}</text>
      </view>
      <view>
        <button 
          v-if="gig.is_full"
          class="bg-disable text-inverse text-sm font-bold py-2 px-4 rounded-full cursor-not-allowed"
          disabled
        >
          已满
        </button>
        <button 
          v-else
          class="bg-primary text-inverse text-sm font-bold py-2 px-4 rounded-full"
          @tap.stop="handleApply"
        >
          报名
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { Gig } from "@/types/gig";
import { formatSalary } from "@/utils/gig";
import { formatWorkTimeRange } from "@/utils/format";

interface Props {
  gig: Gig;
}

const props = defineProps<Props>();

const emit = defineEmits<{ 
  apply: [gig: Gig];
  cardClick: [gig: Gig];
}>();

const formattedSalary = computed(() => {
  return formatSalary(props.gig.salary, props.gig.salary_unit);
});

const formattedWorkTime = computed(() => {
  return (
    formatWorkTimeRange(props.gig.start_time, props.gig.end_time, "simple") ||
    "时间待定"
  );
});

const formattedWorkDuration = computed(() => {
  const minutes = props.gig.work_duration;
  if (!minutes || minutes <= 0) return "待定";

  if (minutes < 60) {
    return `${minutes}分钟`;
  } else if (minutes < 1440) {
    // 小于24小时
    const hours = Math.round((minutes / 60) * 10) / 10;
    return `${hours}小时`;
  } else {
    const days = Math.round((minutes / 1440) * 10) / 10;
    return `${days}天`;
  }
});

const truncatedDescription = computed(() => {
  if (!props.gig.description) return "";
  return props.gig.description.length > 60
    ? props.gig.description.substring(0, 60) + "..."
    : props.gig.description;
});

const handleApply = () => {
  emit("apply", props.gig);
};

const handleCardClick = () => {
  emit("cardClick", props.gig);
};
</script>

<style lang="scss" scoped>
.gig-card {
  position: relative;
  background: var(--bg-card);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
  }

  // 紧急标识
  .urgent-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(135deg, var(--text-red), #ff6b6b);
    padding: 8rpx 20rpx 8rpx 32rpx;
    border-radius: 0 24rpx 0 24rpx;
    z-index: 2;

    .urgent-text {
      color: white;
      font-size: 24rpx;
      font-weight: 600;
    }

    &::before {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 0;
      height: 0;
      border-left: 12rpx solid transparent;
      border-bottom: 12rpx solid rgba(245, 44, 55, 0.8);
    }
  }

  .card-content {
    padding: 32rpx;
  }

  // 头部区域
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32rpx;

    .title-section {
      flex: 1;
      margin-right: 24rpx;

      .gig-title {
        font-size: 36rpx;
        font-weight: 600;
        color: var(--text-base);
        line-height: 1.3;
        margin-bottom: 8rpx;
        display: block;
      }

      .gig-desc {
        font-size: 26rpx;
        color: var(--text-secondary);
        line-height: 1.4;
      }
    }

    .salary-section {
      flex-shrink: 0;
      text-align: right;

      .salary-amount {
        font-size: 40rpx;
        font-weight: 700;
        color: var(--primary);
      }
    }
  }

  // 关键信息网格
  .key-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24rpx 16rpx;
    margin-bottom: 32rpx;

    .info-item {
      display: flex;
      align-items: flex-start;
      gap: 12rpx;

      .info-icon {
        font-size: 28rpx;
        color: var(--primary);
        margin-top: 2rpx;
        flex-shrink: 0;
      }

      .info-content {
        flex: 1;
        min-width: 0;

        .info-label {
          font-size: 22rpx;
          color: var(--text-info);
          margin-bottom: 2rpx;
          display: block;
        }

        .info-value {
          font-size: 26rpx;
          color: var(--text-base);
          font-weight: 500;
          word-break: break-all;
        }
      }
    }
  }

  // 标签区域
  .tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-bottom: 32rpx;

    .tag {
      background: var(--bg-tag);
      color: var(--text-secondary);
      padding: 6rpx 12rpx;
      border-radius: 16rpx;
      font-size: 22rpx;
      font-weight: 500;

      &.tag-requirement {
        background: var(--bg-primary-light);
        color: var(--primary);
      }
    }
  }

  // 底部区域
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16rpx;

    .footer-info {
      flex: 1;
      min-width: 0;

      .publish-time {
        font-size: 22rpx;
        color: var(--text-info);
        margin-bottom: 4rpx;
        display: block;
      }

      .apply-count {
        font-size: 24rpx;
        color: var(--text-secondary);
        font-weight: 500;
      }
    }

    .action-section {
      flex-shrink: 0;

      .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 22rpx;
        font-weight: 600;

        .status-text {
          color: inherit;
        }

        &.status-pending {
          background: var(--bg-warning-light);
          color: var(--text-yellow);
        }

        &.status-confirmed {
          background: var(--bg-success-light);
          color: var(--text-green);
        }

        &.status-rejected {
          background: var(--bg-danger-light);
          color: var(--text-red);
        }
      }

      .apply-button {
        background: linear-gradient(135deg, var(--primary), #ff8533);
        color: white;
        border: none;
        border-radius: 32rpx;
        padding: 16rpx 32rpx;
        font-size: 26rpx;
        font-weight: 600;
        box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.3);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
          box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.4);
        }

        .apply-text {
          color: white;
        }
      }

      .detail-button {
        background: var(--bg-tag);
        color: var(--text-base);
        border: none;
        border-radius: 32rpx;
        padding: 16rpx 32rpx;
        font-size: 26rpx;
        font-weight: 500;
        transition: all 0.3s ease;

        &:active {
          background: var(--text-info);

          .detail-text {
            color: white;
          }
        }

        .detail-text {
          color: var(--text-base);
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 750rpx) {
    .key-info-grid {
      grid-template-columns: 1fr;
      gap: 20rpx;
    }

    .card-footer {
      flex-direction: column;
      align-items: stretch;
      gap: 20rpx;

      .action-section {
        align-self: stretch;

        .apply-button,
        .detail-button {
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}
</style>
