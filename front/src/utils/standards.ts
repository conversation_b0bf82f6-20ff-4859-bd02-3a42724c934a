/**
 * 标准化常量工具函数
 * 基于 @/constants/standards.ts 中的全局常量提供工具函数
 */
import {
  genderOptions,
  experienceOptions,
  educationOptions,
  GENDER,
  EXPERIENCE,
  EDUCATION
} from '@/constants/standards';

// ====================================================================
// 文本获取函数
// ====================================================================

/**
 * 获取性别文本
 * @param gender 性别值
 */
export function getGenderText(gender: number): string {
  const option = genderOptions.find(opt => opt.code === gender);
  return option ? option.label : '不限';
}

/**
 * 获取经验文本
 * @param experience 经验值
 */
export function getExperienceText(experience: number): string {
  const option = experienceOptions.find(opt => opt.code === experience);
  return option ? option.label : '不限';
}

/**
 * 获取学历文本
 * @param education 学历值
 */
export function getEducationText(education: number): string {
  const option = educationOptions.find(opt => opt.code === education);
  return option ? option.label : '不限';
}

// ====================================================================
// 映射对象 (向后兼容)
// ====================================================================

/**
 * 性别映射对象 (向后兼容)
 */
export const GENDER_MAP: Record<number, string> = {
  [GENDER.UNLIMITED]: '不限',
  [GENDER.MALE]: '男',
  [GENDER.FEMALE]: '女'
};

/**
 * 经验映射对象 (向后兼容)
 */
export const EXPERIENCE_MAP: Record<number, string> = {
  [EXPERIENCE.UNLIMITED]: '不限',
  [EXPERIENCE.FRESH_GRADUATE]: '应届毕业生',
  [EXPERIENCE.UNDER_1_YEAR]: '1年以下',
  [EXPERIENCE.YEARS_1_TO_3]: '1-3年',
  [EXPERIENCE.YEARS_3_TO_5]: '3-5年',
  [EXPERIENCE.YEARS_5_TO_10]: '5-10年',
  [EXPERIENCE.OVER_10_YEARS]: '10年以上'
};

/**
 * 学历映射对象 (向后兼容)
 */
export const EDUCATION_MAP: Record<number, string> = {
  [EDUCATION.UNLIMITED]: '不限',
  [EDUCATION.JUNIOR_HIGH]: '初中及以下',
  [EDUCATION.HIGH_SCHOOL]: '高中/中专',
  [EDUCATION.ASSOCIATE]: '大专',
  [EDUCATION.BACHELOR]: '本科',
  [EDUCATION.MASTER]: '硕士'
};

// ====================================================================
// 反向映射 (用于数据提交)
// ====================================================================

/**
 * 创建反向映射的工具函数
 */
function createReverseMap(map: Record<number, string>): Record<string, number> {
  const reverseMap: Record<string, number> = {};
  for (const key in map) {
    if (Object.prototype.hasOwnProperty.call(map, key)) {
      reverseMap[map[key]] = Number(key);
    }
  }
  return reverseMap;
}

export const REVERSE_GENDER_MAP = createReverseMap(GENDER_MAP);
export const REVERSE_EXPERIENCE_MAP = createReverseMap(EXPERIENCE_MAP);
export const REVERSE_EDUCATION_MAP = createReverseMap(EDUCATION_MAP);

// ====================================================================
// 表单选项 (用于 Picker 组件等)
// ====================================================================

/**
 * 创建表单选项的工具函数
 */
function createFormOptions(map: Record<number, string>): { label: string; value: number }[] {
  return Object.entries(map).map(([value, label]) => ({
    label,
    value: Number(value),
  }));
}

/**
 * 性别选项 (用于表单)
 */
export const GENDER_OPTIONS = createFormOptions(GENDER_MAP);

/**
 * 经验选项 (用于表单)
 */
export const EXPERIENCE_OPTIONS = createFormOptions(EXPERIENCE_MAP);

/**
 * 学历选项 (用于表单)
 */
export const EDUCATION_OPTIONS = createFormOptions(EDUCATION_MAP);