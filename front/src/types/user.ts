// src/types/user.ts

/**
 * 用户信息 - 与后端UserProfile保持一致
 */
export interface UserInfo {
    // 基本信息
    uid: string;
    nickname: string;
    avatar: string;
    gender: number;
    birthday: string | null;
    phone: string;
    status: number;
    createdAt: string;
    
    // 认证信息
    isVerified: boolean;
    personalVerificationId: number;
    enterpriseId: number;
    
    // 会员信息
    points: number;
    
    // 兼容性字段（保留用于向后兼容）
    id?: number; // 可选，用于兼容旧代码
    personalVerified?: boolean; // 可选，用于兼容旧代码
    
    // 扩展字段（前端特有）
    email?: string;
    isVip?: boolean;
    vipExpiresAt?: string;
    isSuperAdmin?: boolean;
    posts?: number;
    followers?: number;
    following?: number;
    balance?: number;
    updatedAt?: string;
    enterpriseVerified?: boolean;
    memberLevel?: number;
}