# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a cross-platform O2O local services application targeting tier 3-4 cities and county-level users. The project consists of a UniApp frontend (supports WeChat Mini Program, H5, App) and a Go backend with clean architecture.

**Core Features:**
- Job recruitment and job seeking
- Housing rental and sales (new/second-hand)
- Dating and social networking  
- Gig work services (housekeeping, repair, etc.)
- Real-time messaging
- Payment integration

## Architecture

### Frontend (front/)
- **Framework**: Vue 3 + TypeScript + UniApp CLI
- **State Management**: Pinia with persistence
- **UI Libraries**: ThorUI, UnoCSS, uni-ui
- **Network**: Alova + uni-network
- **Build Tool**: Vite

### Backend (backend/)
- **Framework**: Go 1.24 + Gin
- **Architecture**: Clean Architecture (Controller → Service → Repository)
- **Database**: PostgreSQL + GORM
- **Cache**: Redis
- **Dependency Injection**: Google Wire
- **Logging**: Zerolog with structured JSON logs
- **Config**: Viper with YAML configs

## Common Development Commands

### Frontend Commands (run from front/)
```bash
# Development
pnpm run dev:mp-weixin    # WeChat Mini Program
pnpm run dev:h5           # H5 web version
pnpm run dev:mp-alipay    # Alipay Mini Program

# Build
pnpm run build:mp-weixin  # Build WeChat Mini Program
pnpm run build:h5         # Build H5
pnpm run type-check       # TypeScript type checking

# Package management
pnpm install              # Install dependencies
```

### Backend Commands (run from backend/)
```bash
# Development
make dev                  # Hot reload development with air
make run                  # Direct run without hot reload
make build               # Build binary
make test                # Run all tests

# Code Quality
make check               # Quick quality check (fmt, imports, vet, tidy)
make check-all          # Full quality check (includes static analysis, security)
make fmt                # Format code
make vet                # Static analysis
make staticcheck        # Advanced static analysis

# Database
make migrate-up         # Run database migrations
make migrate-down       # Rollback migrations

# Docker
make docker-build       # Build Docker image
make docker-run         # Run with docker-compose
make docker-stop        # Stop docker-compose

# Testing & Coverage
make test-coverage      # Generate test coverage report
make benchmark          # Run benchmarks

# Tools Installation
make install-tools      # Install development tools (air, staticcheck, etc.)
```

## Project Structure Key Points

### Frontend Structure
```
front/src/
├── pages/              # Page components organized by feature
├── components/         # Reusable components (common/, feature-specific/)
├── stores/            # Pinia stores (global.ts, user.ts, job.ts)
├── api/               # API interface definitions
├── utils/             # Utilities (http.ts, alova.ts, request.ts)
├── constants/         # Constants and static data
└── types/             # TypeScript type definitions
```

### Backend Structure
```
backend/
├── cmd/server/        # Application entry point
├── internal/          # Private application code
│   ├── api/           # HTTP layer (controllers, middleware, routing)
│   ├── service/       # Business logic layer
│   ├── repository/    # Data access layer
│   ├── model/         # Database models (GORM structs)
│   ├── types/         # API request/response types
│   └── utils/         # Internal utilities
├── pkg/               # Reusable packages
│   ├── config/        # Configuration management
│   ├── database/      # Database connection
│   ├── cache/         # Redis cache
│   ├── logger/        # Structured logging
│   ├── jwt/           # JWT authentication
│   ├── response/      # Unified API responses
│   └── wechat/        # WeChat integration
└── configs/           # Configuration files
```

## Development Guidelines

### API Design Standards

#### Core Principles
1. **Resource-Oriented**: APIs focus on nouns (resources), not verbs (actions)
2. **Explicit Methods**: Use HTTP methods (`GET`, `POST`, `PUT`, `PATCH`, `DELETE`) to express operations
3. **Unified Case**: **Globally enforce `snake_case`** for URL paths, query parameters, and JSON data
4. **Stateless**: Every request must contain all necessary information
5. **Versioning**: All APIs must be versioned via path: `/api/v1/...`

#### URL Structure Convention

**Resource Naming:**
- **MUST** use plural nouns for resource collections
- ✅ Correct: `/gigs`, `/users`, `/job-posts`
- ❌ Incorrect: `/gig`, `/userList`, `/create_job_post`

**Path Parameters:**
- **MUST** use curly braces `{}` and `snake_case`
- ✅ Correct: `/gigs/{gig_id}/comments/{comment_id}`
- ❌ Incorrect: `/gigs/:gigId`

**Query Parameters:**
- **MUST** use `snake_case`
  - Example: `GET /gigs?sort=-created_at,title`
- **Filtering**: Use parameter names directly
  - Example: `GET /gigs?status=active&category_id=3`
- **Pagination**: `page` and `page_size`
  - Example: `GET /gigs?page=1&page_size=20`
- **Sparse Fieldsets**: `fields` parameter for bandwidth optimization
  - Example: `GET /gigs?fields=id,title,salary_min,salary_max`

#### JSON Response Standards

**Keys**: **MUST** use `snake_case`
**Timestamps**: **MUST** use `YYYY-MM-DD HH:mm:ss` format: `2025-07-15 21:07:50`

#### Frontend TypeScript Naming Convention

**Interfaces/Types**: **MUST** use `PascalCase`
- `Gig`: Core gig object
- `GigCreateRequest`: Request body for creating gig
- `GigUpdateRequest`: Request body for updating gig
- `PaginatedGigsResponse`: Paginated list response

**Variables/Properties**: **MUST** use `camelCase`
- Implement automatic `snake_case` ⇔ `camelCase` transformation in network layer
- Business logic should only deal with `camelCase`
- ✅ Correct: `const gigTitle = gig.jobTitle;`
- ❌ Incorrect: `const gig_title = gig.job_title;` (in business logic)

**Enums**: **MUST** use `PascalCase` for enum name and members
```typescript
export enum GigStatus {
  Active = 'active',
  Paused = 'paused',
  Closed = 'closed',
}
```

- **Type Definitions**: API types go in `backend/internal/types/`, NOT in model directory

### Backend Code Standards
- **Layered Architecture**: Strict Controller → Service → Repository separation
- **Error Handling**: Use `pkg/response` for consistent error responses
- **Database**: Always specify fields in GORM queries, avoid `SELECT *`
- **Logging**: Use structured logging with context (request_id, user_id)
- **Dependency Injection**: Use Google Wire for clean DI
- **Constants**: All constants and enums are defined in `backend/internal/constants/`
- **Paginate**: The pagination method uniformly uses the Paginate in the model. Use example: 'db.Scopes(Paginate(page,pageSize)).find (&users)'

### Frontend Standards  
- **Component Organization**: Break down large files into smaller components
- **State Management**: Use Pinia stores for global state
- **API Calls**: Use Alova for network requests with proper error handling
- **Styling**: Combine UnoCSS utilities with SCSS for complex styles
- **Constants**: Define all constants in `front/src/constants/`
- **Types**: Define all types in `front/src/types/`
- **Utils**: Define all utils in `front/src/utils/`

- Do not repeatedly encapsulate functions or transition designs. Use concise, efficient, and highly available code


## Key Configuration Files

### Backend Config (backend/configs/config.yaml)
Contains database, Redis, JWT, payment (WeChat/Alipay), storage (Qiniu), and SMS (Aliyun) configurations.

### Frontend Config (front/vite.config.ts)
- Environment variables from `front/env/`
- Auto-imports for Vue and uni-app APIs
- UnoCSS for utility-first CSS
- Path aliases (`@` → `src/`)

## Testing & Quality

### Backend Testing
```bash
make test              # All tests
make test-coverage     # Coverage report (generates coverage.html)
make benchmark         # Performance benchmarks
```

### Code Quality Tools
- **gofmt**: Code formatting
- **go vet**: Basic static analysis
- **staticcheck**: Advanced static analysis
- **gosec**: Security analysis
- **errcheck**: Error handling verification

## WeChat Mini Program Notes
- Configured for Skyline rendering engine
- Pay attention to compatibility requirements
- Reference: https://developers.weixin.qq.com/miniprogram/dev/framework/runtime/skyline/introduction.html

## Database Operations
- Use migrations in `backend/migrations/`
- Follow proper indexing for query performance
- Wrap multiple operations in transactions
- Always handle `gorm.ErrRecordNotFound`

## Data Type Standards

### User ID Field Type
**IMPORTANT**: 
All user ID fields (userId, user_id, etc.) throughout the entire codebase MUST use `uint` type:
The way to obtain the userID is userID := GetUserID(ctx)

**Backend (Go):**
```go
// ✅ Correct
UserID uint `gorm:"not null;index" json:"user_id"`

// ❌ Wrong
UserID uint64 `gorm:"not null;index" json:"user_id"`
UserID int `gorm:"not null;index" json:"user_id"`
```

**Frontend (TypeScript):**
```typescript
// ✅ Correct
userId: number

// ❌ Wrong  
userId: string
```

**Why this standard:**
- BaseModel.ID uses `uint` type
- Foreign key relationships must match the referenced primary key type
- Prevents type mismatch errors in wire generation and GORM queries
- Ensures consistency across all modules (gig, user, etc.)

**Apply to all models:**
- User model primary key: `uint`
- All foreign key references to users: `uint`
- API request/response types: `number` (TypeScript)

### Category Removal Standard
The gig module does NOT use categories. Remove all category-related:
- Database tables and fields
- API endpoints and services  
- Frontend components and types
- Backend models and repositories

## Wire Dependency Injection
The backend uses Google Wire for dependency injection. Key files:
- `backend/internal/api/wire.go`: Main DI configuration
- Generated code in `wire_gen.go` files
- Provider sets in each layer (repository, service, controller)

## Frontend Color System & Styling Guidelines

### Color Variables (Use CSS Variables)
The project has a comprehensive color system defined in `front/src/styles/app.css`:

**Primary Colors:**
```css
--primary-50 to --primary-950   /* Full primary color scale */
--primary: var(--primary-600)   /* Main primary color: #ff6d00 */
```

**Text Colors:**
```css
--text-base: #212529            /* Primary text */
--text-secondary: #515359       /* Secondary text */
--text-info: #8e8e93            /* Info text */
--text-grey: #a1a1aa            /* Subtle text */
--text-disable: #d4d4d8         /* Disabled text */
--text-inverse: #feffff         /* White text */

/* Semantic Colors */
--text-red: #f52c37             /* Error/danger */
--text-green: #22c55e           /* Success */
--text-blue: #3b82f6            /* Info */
--text-yellow: #eab308          /* Warning */
--text-purple: #8b5cf6          /* Purple accent */
```

**Background Colors:**
```css
--bg-page: #f4f5f9              /* Page background */
--bg-card: #feffff              /* Card background */
--bg-tag: #f3f5f7               /* Tag background */
--bg-search: #f1f2f7            /* Search input background */
--bg-input: #f2f2f7             /* Form input background */
--bg-mask: rgba(0, 0, 0, 0.5)   /* Modal overlay */
```

**Functional Background Colors:**
```css
--bg-primary-light: rgba(255, 109, 0, 0.1)
--bg-success-light: rgba(34, 197, 94, 0.1)
--bg-warning-light: rgba(234, 179, 8, 0.1)
--bg-danger-light: rgba(220, 38, 38, 0.1)
--bg-info-light: rgba(59, 130, 246, 0.1)
```

### Design System Tokens

**Spacing System:**
```css
--spacing-4: 8rpx to --spacing-40: 80rpx
```

**Border Radius:**
```css
--radius-sm: 8rpx
--radius: 16rpx
--radius-lg: 24rpx
--radius-xl: 32rpx
--radius-xxl: 40rpx
```

**Typography Scale:**
```css
--font-size-xs: 24rpx
--font-size-sm: 26rpx
--font-size-base: 28rpx
--font-size-md: 30rpx
--font-size-lg: 32rpx
--font-size-xl: 36rpx
--font-size-xxl: 40rpx
```

### Styling Best Practices

1. **Always use CSS variables** instead of hardcoded colors:
   ```scss
   // ✅ Good
   color: var(--text-base);
   background: var(--bg-card);
   
   // ❌ Bad
   color: #212529;
   background: #feffff;
   ```

2. **Use utility classes** when available:
   ```vue
   <!-- ✅ Good -->
   <view class="text-primary bg-card rounded-lg">
   
   <!-- ❌ Avoid custom styles when utilities exist -->
   <view style="color: #ff6d00; background: #feffff; border-radius: 24rpx;">
   ```

3. **Follow the tag system** for consistent labeling:
   ```vue
   <!-- Basic tags -->
   <view class="tag">标签</view>
   <view class="tag tag-sm">小标签</view>
   
   <!-- Semantic tags -->
   <view class="tag tag-primary">主要</view>
   <view class="tag tag-success">成功</view>
   <view class="tag tag-warning">警告</view>
   <view class="tag tag-danger">危险</view>
   
   <!-- Outlined variants -->
   <view class="tag tag-primary-outlined">边框样式</view>
   ```

4. **Use consistent spacing**:
   ```scss
   // Use predefined spacing variables
   padding: var(--spacing-16);
   margin: var(--spacing-12) var(--spacing-8);
   ```

5. **Shadow system**:
   ```vue
   <view class="shadow-sm">轻微阴影</view>
   <view class="shadow">标准阴影</view>
   <view class="shadow-lg">较强阴影</view>
   ```

### Component Styling Guidelines

- Always use `var(--color-name)` for colors
- Maintain 4:1 contrast ratio for accessibility
- Use semantic color variables for consistent theming
- Follow the 8rpx grid system for spacing
- Prefer utility classes over custom CSS when possible
- Keep component styles modular and reusable